<template>
  <div class="full-height vertical-flex-box">
    <!-- 自定义查询条件 -->
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="orderCode" :label="$t('采购订单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.orderCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="orderStatus" :label="$t('订单状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.orderStatus"
            css-class="rule-element"
            :data-source="isSup ? orderStatusSupList : orderStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="businessType" :label="$t('业务类型')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.businessType"
            css-class="rule-element"
            :data-source="businessTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="itemCodeStr" :label="$t('物料编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemCodeStr"
            :show-clear-button="true"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemName" :label="$t('物料名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemName"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="itemNo" :label="$t('采购订单行号')" label-style="top">
          <mt-input
            v-model="searchFormModel.itemNo"
            :show-clear-button="true"
            :placeholder="$t('精准查询')"
          />
        </mt-form-item>
        <mt-form-item prop="contractCode" :label="$t('合同编号')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractCode"
            :show-clear-button="true"
            :placeholder="$t('请输入合同编号')"
          />
        </mt-form-item>
        <mt-form-item prop="contractName" :label="$t('合同名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.contractName"
            :show-clear-button="true"
            :placeholder="$t('请输入合同名称')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryDriverType" :label="$t('发货驱动类型')" label-style="top">
          <mt-select
            v-model="searchFormModel.deliveryDriverType"
            css-class="rule-element"
            :data-source="deliveryDriverTypeList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
          <mt-select
            v-model="searchFormModel.companyCode"
            css-class="rule-element"
            :data-source="companyList"
            :fields="{ text: 'text', value: 'orgCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择公司')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCodeList" :label="$t('工厂')" label-style="top">
          <RemoteAutocomplete
            v-model="searchFormModel.siteCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item :label="$t('入库仓库')" prop="warehouseCodeList">
          <RemoteAutocomplete
            v-model="searchFormModel.warehouseCodeList"
            url="/srm-purchase-pv/tenant/pv/warehouse/page/query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'warehouseName', value: 'warehouseCode' }"
            params-key="fuzzyParam"
          />
        </mt-form-item>
        <mt-form-item prop="categoryCode" :label="$t('品类编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryCode"
            :show-clear-button="true"
            :placeholder="$t('精准查询')"
          />
        </mt-form-item>
        <mt-form-item prop="categoryName" :label="$t('品类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.categoryName"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="largeCategoryName" :label="$t('大类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.largeCategoryName"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="mediumCategoryName" :label="$t('中类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.mediumCategoryName"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item prop="smallCategoryName" :label="$t('小类名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.smallCategoryName"
            :show-clear-button="true"
            :placeholder="$t('支持模糊查询')"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierCode" :label="$t('供应商编码')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :show-clear-button="true"
            :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierName" :label="$t('供应商名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="publishStatus" :label="$t('发布状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.publishStatus"
            css-class="rule-element"
            :data-source="publishStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="preDeliveryQty" :label="$t('待发货数量')" label-style="top">
          <div style="display: flex; align-items: center">
            <mt-select
              v-model="searchFormModel.symbol"
              :data-source="symbolOptions"
              :fields="{ text: 'text', value: 'value' }"
              :show-clear-button="true"
              :placeholder="$t('请选择')"
              style="width: 30%; height: 30px; line-height: 30px"
            ></mt-select>
            <mt-input-number
              v-model="searchFormModel.num"
              :show-clear-button="true"
              :placeholder="$t('请输入')"
              min="0"
              :show-spin-button="false"
            />
          </div>
        </mt-form-item>
        <mt-form-item prop="sealSignFlag" :label="$t('是否用印签署')" label-style="top">
          <mt-select
            v-model="searchFormModel.sealSignFlag"
            css-class="rule-element"
            :data-source="sealSignFlagList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="approvalStatus" :label="$t('审批状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.approvalStatus"
            css-class="rule-element"
            :data-source="approvalStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="urgentStatus" :label="$t('加急状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.urgentStatus"
            css-class="rule-element"
            :data-source="urgentStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryStatus" :label="$t('发货状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.deliveryStatus"
            css-class="rule-element"
            :data-source="deliveryStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="receiveStatus" :label="$t('收货状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.receiveStatus"
            css-class="rule-element"
            :data-source="receiveStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="closeStatus" :label="$t('关闭状态')" label-style="top">
          <mt-select
            v-model="searchFormModel.closeStatus"
            :data-source="closeStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="deliveryCompletedFlag" :label="$t('交货已完成标识')" label-style="top">
          <mt-select
            v-model="searchFormModel.deliveryCompletedFlag"
            :data-source="yesOrNoList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="orderSource" :label="$t('订单来源')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.orderSource"
            css-class="rule-element"
            :data-source="dataSourceList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="currencyName" :label="$t('币种名称')" label-style="top">
          <mt-input
            v-model="searchFormModel.currencyName"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="tradeMethod" :label="$t('贸易方式')" label-style="top">
          <mt-input
            v-model="searchFormModel.tradeMethod"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="publishTime" :label="$t('发布时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.publishTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'publishTime')"
          />
        </mt-form-item>
        <mt-form-item prop="feedbackTime" :label="$t('反馈时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.feedbackTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'feedbackTime')"
          />
        </mt-form-item>
        <mt-form-item prop="syncErpStatus" :label="$t('同步极光状态')" label-style="top">
          <mt-multi-select
            v-model="searchFormModel.syncErpStatus"
            css-class="rule-element"
            :data-source="syncOaStatusList"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="sourceCode" :label="$t('来源单号')" label-style="top">
          <mt-input
            v-model="searchFormModel.sourceCode"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="purchaseRemark" :label="$t('采方备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.purchaseRemark"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="supplierRemark" :label="$t('供方备注')" label-style="top">
          <mt-input
            v-model="searchFormModel.supplierRemark"
            :show-clear-button="true"
            :placeholder="$t('请输入')"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('最后更新时间')" label-style="top">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :allow-edit="false"
            :placeholder="$t('请选择')"
            :open-on-focus="true"
            @change="(e) => handleDateChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <!-- 表格 -->
    <sc-table
      ref="sctableRef"
      grid-id="25cffd9d-793c-8bdb-bbf4-c1ed40f43c91"
      :loading="loading"
      :is-show-refresh-bth="true"
      :columns="listColumns"
      :table-data="tableData"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 分页 -->
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import mixin from './../config/mixin'
import {
  detailToolbar,
  deliveryDriverTypeList,
  orderStatusList,
  orderStatusSupList,
  publishStatusList,
  sealSignFlagList,
  businessTypeList,
  urgentStatusList,
  deliveryStatusList,
  receiveStatusList,
  warehouseStatusList,
  dataSourceList,
  approvalStatusList,
  syncOaStatusList,
  closeStatusList,
  signStatusList,
  yesOrNoList,
  symbolOptions
} from './../config/index'
import XEUtils from 'xe-utils'

export default {
  components: {
    ScTable,
    CollapseSearch,
    RemoteAutocomplete
  },
  mixins: [mixin],
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 20,
        pageSizes: [20, 50, 200, 500, 1000, 2000]
      },
      pageInfo: {
        size: 20,
        current: 1
      },
      deliveryDriverTypeList,
      orderStatusList,
      orderStatusSupList,
      publishStatusList,
      sealSignFlagList,
      businessTypeList,
      urgentStatusList,
      deliveryStatusList,
      receiveStatusList,
      warehouseStatusList,
      dataSourceList,
      approvalStatusList,
      syncOaStatusList,
      closeStatusList,
      signStatusList,
      yesOrNoList,
      symbolOptions,
      toolbar: [],
      searchFormModel: {},
      tableData: [],
      loading: false,
      isSup: false
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    // 列配置
    listColumns() {
      return [
        {
          width: 50,
          type: 'checkbox',
          fixed: 'left',
          align: 'center'
        },
        // {
        //   width: 70,
        //   field: 'index',
        //   title: this.$t('序号')
        // },
        {
          field: 'orderCode',
          title: this.$t('采购订单号'),
          minWidth: 180,
          slots: {
            default: ({ row, column }) => {
              return [
                <a on-click={() => this.handleClickCellTitle(row, column)}>{row.orderCode}</a>
              ]
            }
          }
        },
        {
          field: 'itemNo',
          title: this.$t('采购订单行号'),
          minWidth: 180
        },
        {
          field: 'orderDate',
          title: this.$t('订单日期'),
          minWidth: 140
        },
        {
          field: 'businessType',
          title: this.$t('业务类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = businessTypeList.find((item) => item.value === row.businessType)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'contractCode',
          title: this.$t('合同编号'),
          minWidth: 180
        },
        {
          field: 'contractName',
          title: this.$t('合同名称'),
          minWidth: 140
        },
        {
          field: 'companyCode',
          title: this.$t('公司编码'),
          minWidth: 120
        },
        {
          field: 'companyName',
          title: this.$t('公司名称'),
          minWidth: 120
        },
        {
          field: 'purchaseOrgCode',
          title: this.$t('采购组织编码'),
          minWidth: 140
        },
        {
          field: 'purchaseOrgName',
          title: this.$t('采购组织名称'),
          minWidth: 140
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码'),
          minWidth: 120
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 120
        },
        {
          field: 'deliveryDriverType',
          title: this.$t('发货驱动类型'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = deliveryDriverTypeList.find(
                (item) => item.value === row.deliveryDriverType
              )
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'sealSignFlag',
          title: this.$t('是否用印签署'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              const selectItem = sealSignFlagList.find((item) => item.value === row.sealSignFlag)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'purchaseUserCode',
          title: this.$t('采购员编码'),
          minWidth: 120
        },
        {
          field: 'purchaseUserName',
          title: this.$t('采购员名称'),
          minWidth: 120
        },
        {
          field: 'orderStatus',
          title: this.$t('订单状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = orderStatusList.find((item) => item.value === row.orderStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'publishStatus',
          title: this.$t('发布状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = publishStatusList.find((item) => item.value === row.publishStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'approvalStatus',
          title: this.$t('审批状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = approvalStatusList.find(
                (item) => item.value === row.approvalStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'signStatus',
          title: this.$t('签署状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = signStatusList.find((item) => item.value === row.signStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'urgentStatus',
          title: this.$t('加急状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = urgentStatusList.find((item) => item.value === row.urgentStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'urgentDate',
          title: this.$t('加急日期'),
          minWidth: 140
        },
        {
          field: 'deliveryStatus',
          title: this.$t('发货状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = deliveryStatusList.find(
                (item) => item.value === row.deliveryStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'receiveStatus',
          title: this.$t('收货状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = receiveStatusList.find((item) => item.value === row.receiveStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'warehouseStatus',
          title: this.$t('入库状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = warehouseStatusList.find(
                (item) => item.value === row.warehouseStatus
              )
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'orderSource',
          title: this.$t('订单来源'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = dataSourceList.find((item) => item.value === row.orderSource)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        // {
        //   field: 'purchaseGroupCode',
        //   title: this.$t('采购组'),
        //   minWidth: 120,
        //   slots: {
        //     default: ({ row }) => {
        //       return [<span>{row.purchaseGroupCode + '-' + row.purchaseGroupName}</span>]
        //     }
        //   }
        // },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 200,
          slots: {
            default: ({ row }) => {
              return [<span>{row.currencyCode + '-' + row.currencyName}</span>]
            }
          }
        },
        {
          field: 'tradeMethod',
          title: this.$t('贸易方式'),
          minWidth: 120
        },
        {
          field: 'paymentType',
          title: this.$t('付款方式'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span>{row.paymentType + '-' + row.paymentTypeName}</span>]
            }
          }
        },
        {
          field: 'paymentTerms',
          title: this.$t('付款条件'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [<span>{row.paymentTerms + '-' + row.paymentTermsName}</span>]
            }
          }
        },
        {
          field: 'publishTime',
          title: this.$t('发布时间'),
          minWidth: 140
        },
        {
          field: 'feedbackTime',
          title: this.$t('反馈时间'),
          minWidth: 140
        },
        {
          field: 'siteCode',
          title: this.$t('工厂'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.siteCode + '-' + row.siteName}</span>]
            }
          }
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码'),
          minWidth: 250,
          slots: {
            default: ({ row }) => {
              return [<span>{row.itemCode + '-' + row.itemName}</span>]
            }
          }
        },
        {
          field: 'brand',
          title: this.$t('品牌'),
          minWidth: 100
        },
        {
          field: 'specificationModel',
          title: this.$t('规格型号'),
          minWidth: 100
        },
        {
          field: 'quantity',
          title: this.$t('订单数量'),
          minWidth: 100
        },
        {
          field: 'preDeliveryQty',
          title: this.$t('待发货数量'),
          minWidth: 120
        },
        {
          field: 'transitQty',
          title: this.$t('在途数量'),
          minWidth: 100
        },
        {
          field: 'warehouseQty',
          title: this.$t('已入库数量'),
          minWidth: 120
        },
        {
          field: 'deliveryQty',
          title: this.$t('已发货数量'),
          minWidth: 120
        },
        {
          field: 'receiveQty',
          title: this.$t('已收货数量'),
          minWidth: 120
        },
        {
          field: 'taxCode',
          title: this.$t('税率编码'),
          minWidth: 100
        },
        {
          field: 'taxName',
          title: this.$t('税率名称'),
          minWidth: 100
        },
        {
          field: 'taxRate',
          title: this.$t('税率'),
          minWidth: 100
        },
        {
          field: 'untaxedPrice',
          title: this.$t('不含税单价'),
          minWidth: 120
        },
        {
          field: 'taxedPrice',
          title: this.$t('含税单价'),
          minWidth: 100
        },
        {
          field: 'untaxedPriceTotal',
          title: this.$t('不含税总价'),
          minWidth: 120
        },
        {
          field: 'taxedPriceTotal',
          title: this.$t('含税总价'),
          minWidth: 100
        },
        {
          field: 'categoryName',
          title: this.$t('品类'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [
                row.categoryCode ? <span>{row.categoryCode + '-' + row.categoryName}</span> : null
              ]
            }
          }
        },
        {
          field: 'largeCategoryName',
          title: this.$t('大类'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [
                row.largeCategoryCode ? (
                  <span>{row.largeCategoryCode + '-' + row.largeCategoryName}</span>
                ) : null
              ]
            }
          }
        },
        {
          field: 'mediumCategoryName',
          title: this.$t('中类'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [
                row.mediumCategoryCode ? (
                  <span>{row.mediumCategoryCode + '-' + row.mediumCategoryName}</span>
                ) : null
              ]
            }
          }
        },
        {
          field: 'smallCategoryName',
          title: this.$t('小类'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [
                row.smallCategoryCode ? (
                  <span>{row.smallCategoryCode + '-' + row.smallCategoryName}</span>
                ) : null
              ]
            }
          }
        },
        {
          field: 'closeStatus',
          title: this.$t('关闭状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = closeStatusList.find((item) => item.value === row.closeStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'deliveryCompletedFlag',
          title: this.$t('交货已完成标识'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = yesOrNoList.find((item) => item.value === row.deliveryCompletedFlag)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'requiredDeliveryDate',
          title: this.$t('要求交期'),
          minWidth: 140
        },
        {
          field: 'baseUnitCode',
          title: this.$t('基本单位'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return [
                row.baseUnitCode ? <span>{row.baseUnitCode + '-' + row.baseUnitName}</span> : null
              ]
            }
          }
        },
        {
          field: 'srmSignFlag',
          title: this.$t('是否SRM签收'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              const selectItem = yesOrNoList.find((item) => item.value === row.srmSignFlag)
              const name = selectItem?.text
              return [<div>{name}</div>]
            }
          }
        },
        {
          field: 'warehouseCode',
          title: this.$t('入库仓库'),
          minWidth: 140,
          slots: {
            default: ({ row }) => {
              return [
                row.warehouseCode ? (
                  <span>{row.warehouseCode + '-' + row.warehouseName}</span>
                ) : null
              ]
            }
          }
        },
        {
          field: 'consignee',
          title: this.$t('收货人'),
          minWidth: 140
        },
        {
          field: 'contactPhone',
          title: this.$t('收货联系方式'),
          minWidth: 140
        },
        {
          field: 'deliveryAddress',
          title: this.$t('配送地址'),
          minWidth: 140
        },
        {
          field: 'promiseTime',
          title: this.$t('承诺日期'),
          minWidth: 140
        },
        {
          field: 'promiseQty',
          title: this.$t('承诺数量'),
          minWidth: 140
        },
        {
          field: 'sourceCode',
          title: this.$t('来源单号'),
          minWidth: 140
        },
        {
          field: 'sourceLineNo',
          title: this.$t('来源单行号'),
          minWidth: 140
        },
        {
          field: 'projectName',
          title: this.$t('项目名称'),
          minWidth: 140
        },
        {
          field: 'projectManager',
          title: this.$t('项目经理'),
          minWidth: 140
        },
        {
          field: 'purchaseRemark',
          title: this.$t('采方备注'),
          minWidth: 140
        },
        {
          field: 'supplierRemark',
          title: this.$t('供方反馈备注'),
          minWidth: 140
        },
        {
          field: 'syncErpStatus',
          title: this.$t('同步极光状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncErpStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncErpDesc',
          title: this.$t('同步极光接口返回'),
          minWidth: 150
        },
        {
          field: 'syncOaStatus',
          title: this.$t('同步OA状态'),
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              const selectItem = syncOaStatusList.find((item) => item.value === row.syncOaStatus)
              const statusName = selectItem?.text
              return [<div>{statusName}</div>]
            }
          }
        },
        {
          field: 'syncOaDesc',
          title: this.$t('同步OA接口返回'),
          minWidth: 140
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 120
        },
        {
          field: 'updateUserName',
          title: this.$t('最后更新人'),
          minWidth: 130
        },
        {
          field: 'updateTime',
          title: this.$t('最后更新时间'),
          minWidth: 140
        }
      ]
    }
  },
  mounted() {
    if (!this.$route.path?.includes('-sup')) {
      this.toolbar = detailToolbar
    } else {
      this.isSup = true
      this.toolbar = [{ code: 'export', name: this.$t('导出'), status: 'info' }]
    }
    this.handleSearch()
  },
  activated() {
    this.handleSearch()
  },
  methods: {
    // 选择公司
    handleCompanyChange(e) {
      console.log('test lint')
      this.$set(this.searchFormModel, 'purchaseOrgCode', null)
      if (e.itemData.id) {
        this.getPurchaseOrgList(e.itemData.id)
      } else {
        this.purchaseOrgList = []
      }
    },
    // 日期格式化
    handleDateChange(e, field) {
      const startField = field + 'Start'
      const endField = field + 'End'
      if (e.startDate) {
        const startDate = XEUtils.toDateString(e.startDate, 'yyyy-MM-dd') + ' 00:00:00'
        const endDate = XEUtils.toDateString(e.endDate, 'yyyy-MM-dd') + ' 23:59:59'
        this.searchFormModel[startField] = XEUtils.timestamp(startDate)
        this.searchFormModel[endField] = XEUtils.timestamp(endDate)
      } else {
        this.searchFormModel[startField] = null
        this.searchFormModel[endField] = null
      }
    },
    // 重置查询条件
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    // 查询
    async handleSearch(type) {
      // 解决修改查询条件后，查询结果有数据但未正常显示的问题（type: pageChange/refresh）
      if (!type) {
        this.pageInfo.current = 1
        this.$refs.pageRef.jumpNum = 1
        this.$refs.pageRef.currentPage = 1
      }
      const params = {
        page: this.pageInfo,
        ...this.searchFormModel
      }
      if (this.searchFormModel.symbol) {
        params.preDeliveryQty = {
          type: this.searchFormModel.symbol,
          num: this.searchFormModel.num || 0
        }
        delete params.symbol
        delete params.num
      }
      // 供方过滤草稿状态
      if (
        this.isSup &&
        !(this.searchFormModel.orderStatus && this.searchFormModel.orderStatus.length)
      ) {
        params.orderStatus = [1, 2, 3, 4, 5, 6, 7]
      }
      this.loading = true
      const res = await this.$API.purchaseOrderMgt
        .queryDetailList(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        this.tableData = res.data?.records || []
        this.tableData?.forEach((item, index) => {
          item.index = index + 1
        }) || []
        this.pageSettings.totalPages = Math.ceil(
          Number(res.data.total) / this.pageSettings.pageSize
        )
        this.pageSettings.totalRecordsCount = Number(res.data?.total)
      }
    },
    // 点击按钮栏
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      if (!['export'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      const ids = []
      for (let index = 0; index < selectedRecords.length; index++) {
        const item = selectedRecords[index]
        if (e.code === 'closeDetailList') {
          // 检查订单状态：不能是草稿、已完成、已关闭状态
          if ([0, 6, 7].includes(item.orderStatus)) {
            this.$toast({
              content: this.$t('仅支持关闭非草稿、非已完成和非已关闭状态的数据！'),
              type: 'warning'
            })
            return
          }
          // 检查发货数量：只有发货数量为0的才可关闭
          if (item.deliveryQty && item.deliveryQty > 0) {
            this.$toast({
              content: this.$t('订单行发货数量大于0，不可关闭！'),
              type: 'warning'
            })
            return
          }
          // 检查是否创建过送货单：订单行没有创建过送货单才可关闭
          // 业务逻辑：如果订单行已发货数量大于0，说明已创建过送货单，不允许关闭
          // 只有发货数量为0的订单行（即没有创建过送货单的）才可以关闭
        }
        ids.push(item.id)
      }
      switch (e.code) {
        case 'viewOA': // 查看OA审批
          if (![1, 2, 3].includes(selectedRecords[0].orderStatus)) {
            this.$toast({
              content: this.$t(
                '仅支持状态为审批中、审批通过和审批拒绝的单据跳转至OA系统查看审批！'
              ),
              type: 'warning'
            })
            return
          }
          this.handleCheckOa(ids)
          break
        case 'closeDetailList': // 关闭
          this.handleOperate(ids, e.code)
          break
        case 'export':
          this.handleExport()
          break
        case 'execute':
          this.handleExecute(ids)
          break
        default:
          break
      }
    },
    handleExecute(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认执行交货已完成？`)
        },
        success: async () => {
          const res = await this.$API.purchaseOrderMgt.executeDeliveryFinish({ ids })
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功！'), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    handleCheckOa(ids) {
      this.$API.purchaseOrderMgt.checkOaList({ id: ids[0] }).then((res) => {
        if (res.code === 200) {
          window.open(res.data)
        } else {
          this.$toast({ content: res.message, type: 'warning' })
        }
      })
    },
    handleExport() {
      const params = { page: { size: 9999, current: 1 }, ...this.searchFormModel } // 筛选条件
      this.$API.purchaseOrderMgt.exportOrderDetailList(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 点击单元格
    handleClickCellTitle(row, column) {
      if (column.field === 'orderCode') {
        this.$router.push({
          name: this.isSup
            ? 'purchase-coordination-pv-detail-sup'
            : 'purchase-coordination-pv-detail',
          query: {
            type: 'detail',
            id: row.orderId,
            refreshId: Date.now()
          }
        })
      }
    },
    // 关闭
    handleOperate(ids, type, extraParams = {}) {
      const tipMap = {
        closeDetailList: '关闭'
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t(`确认${tipMap[type]}选中的数据？`)
        },
        success: async () => {
          const res = await this.$API.purchaseOrderMgt[type]({ ids, ...extraParams })
          if (res.code === 200) {
            this.$toast({ content: this.$t(`${tipMap[type]}成功！`), type: 'success' })
            this.handleSearch()
          }
        }
      })
    },
    // 切换page
    handleCurrentChange(currentPage) {
      this.pageInfo.current = currentPage
      this.$refs.pageRef.jumpNum = currentPage
      this.handleSearch('pageChange')
    },
    // 切换pageSize
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.pageInfo = {
        size: pageSize,
        current: 1
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  padding: 8px 8px 0 8px;
  background: #fff;
}
</style>
