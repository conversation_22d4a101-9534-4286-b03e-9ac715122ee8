<template>
  <div style="height: 100%">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleSearchReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="demandCode" :label="$t('需求单编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.demandCode"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                type="multipleChoice"
                :placeholder="$t('请选择状态')"
                :show-select-all="true"
                :show-clear-button="true"
                :data-source="statusOptions"
                :fields="{ text: 'text', value: 'value' }"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteAuthFuzzyUrl"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                params-key="fuzzyParam"
                records-position="data"
              />
            </mt-form-item>
            <mt-form-item prop="sendAddress" :label="$t('送货地址')" label-style="top">
              <mt-input
                v-model="searchFormModel.sendAddress"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'createTime')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('最后更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
            <mt-form-item prop="updateTime" :label="$t('更新时间')">
              <mt-date-range-picker
                v-model="searchFormModel.updateTime"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                @change="(e) => dataTimeChange(e, 'updateTime')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData, statusOptions } from '../config/index'
import { BASE_TENANT } from '@/utils/constant'
import dayjs from 'dayjs'

export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      // 0-新建,1-已提交,2-已调料,3-已取消
      statusOptions,
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Import',
                  title: this.$t('新增')
                },
                {
                  id: 'edit',
                  icon: 'icon_solid_Import',
                  title: this.$t('编辑')
                },
                {
                  id: 'cancel',
                  icon: 'icon_solid_Import',
                  title: this.$t('取消')
                }
              ],
              ['Refresh', 'Setting']
            ]
          },
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          gridId: '5cd44e34-3682-45c4-bdc0-eedf0382e463',
          grid: {
            pageSettings: {
              currentPage: 1,
              pageSize: 20,
              pageSizes: [10, 20, 50, 100, 200, 500, 1000],
              totalRecordsCount: 0
            },
            columnData: columnData,
            asyncConfig: {
              url: `${BASE_TENANT}/steelDemand/querySupplierPage`
            }
          }
        }
      ]
    }
  },
  methods: {
    dataTimeChange(e, flag) {
      if (e.startDate) {
        this.searchFormModel[flag + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[flag + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[flag + 'Start'] = null
        this.searchFormModel[flag + 'End'] = null
      }
    },
    getUnix(val) {
      return new Date(val).getTime()
    },
    handleClickCellTitle(e) {
      if (e.field === 'demandCode') {
        this.$router.push({
          path: `steel-demand-management-detail`,
          query: {
            timeStamp: new Date().getTime(),
            type: 'detail',
            id: e.data.id
          }
        })
      }
    },
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()
      if (toolbar.id == 'add') {
        this.$router.push({
          path: `steel-demand-management-detail`,
          query: {
            timeStamp: new Date().getTime(),
            type: 'add'
          }
        })
      } else if (toolbar.id == 'edit') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('只能选择一行'), type: 'warning' })
          return
        }
        this.$router.push({
          path: `steel-demand-management-detail`,
          query: {
            timeStamp: new Date().getTime(),
            type: 'edit',
            id: selectedRecords[0]['id']
          }
        })
      } else if (toolbar.id == 'cancel') {
        const cancelIds = []
        selectedRecords.forEach((i) => {
          cancelIds.push(i.id)
        })
        this.handleCancel(cancelIds)
      }
    },
    handleCancel(ids) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认取消？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.thirdPartyVMICollaboration
            .cancelSteelDemandApi({ ids })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t(`操作成功`),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleSearchReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
          if (key === 'status') {
            this.searchFormModel[key] = []
          }
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    // 加载开始
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
