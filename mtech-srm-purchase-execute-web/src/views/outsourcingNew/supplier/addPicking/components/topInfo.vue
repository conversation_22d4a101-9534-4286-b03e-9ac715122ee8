<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="itemcon mr20" v-if="!dis">
        <span class="infos" style="margin-right: 10px">{{ $t('创建方式：') }}</span>
        <mt-radio v-model="topInfo.createType" :data-source="radioData" @input="onchang"></mt-radio>
      </div>
      <!-- <div v-else> -->
      <div v-if="dis" class="status mr20">
        {{ typeInfo(topInfo.status) }}
      </div>
      <div class="infos mr20" v-if="dis">{{ $t('创建人') }}：{{ topInfo.createUserName }}</div>
      <div class="infos" v-if="dis">{{ $t('创建日期：') }}{{ topInfo.createTime }}</div>
      <div class="infos mr20" v-if="dis">{{ $t('单据编号：') }}{{ topInfo.receiveOrderCode }}</div>
      <div class="infos mr20" v-if="topInfo.buyerApproveUserName && dis">
        {{ $t('采方确认人') }}：{{ topInfo.buyerApproveUserName }}
      </div>
      <div class="infos mr20" v-if="topInfo.buyerApproveUserName && dis">
        {{ $t('采方确认时间') }}：{{ dateFormat(topInfo.buyerApproveDate) }}
      </div>
      <div class="infos mr20" v-if="topInfo.materialApproveUserName && dis">
        {{ $t('供方确认人') }}：{{ topInfo.materialApproveUserName }}
      </div>
      <div class="infos mr20" v-if="topInfo.materialApproveUserName && dis">
        {{ $t('供方确认时间') }}：{{ dateFormat(topInfo.materialApproveDate) }}
      </div>
      <!-- </div> -->
      <div class="middle-blank"></div>
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="getItemInfo"
        v-if="!dis && topInfo.createType === '1'"
        >{{ $t('获取物料信息') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="newSave"
        v-if="!dis && topInfo.createType === '1'"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="newSubmit"
        v-if="!dis && topInfo.createType === '1'"
        >{{ $t('提交') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="save"
        v-if="!dis && topInfo.createType === '2'"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        :is-primary="true"
        @click="submit"
        v-if="!dis && topInfo.createType === '2'"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="expandChange">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <div class="main-bottom" v-show="topInfo.createType === '2' || entryType == 'readonly'">
      <mt-form ref="ruleForm" :model="topInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item :label="$t('公司')" prop="customerEnterpriseId">
          <mt-select
            ref="customerEnterpriseId"
            v-model="topInfo.customerEnterpriseId"
            :open-dispatch-change="true"
            :allow-filtering="true"
            :disabled="dis || isDisabled2"
            :filtering="getSourceCompany1"
            :fields="{ text: 'name', value: 'code' }"
            :data-source="dataArr3"
            @change="companyChange"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <mt-input v-if="dis" disabled type="text" v-model="receiveSite"></mt-input>
          <debounce-filter-select
            v-else
            v-model="topInfo.siteCode"
            :disabled="dis || isDisabled2"
            :request="getSourceAvailable"
            :value-template="siteCodeValueTemplate"
            :is-active="true"
            :data-source="siteOptions"
            :fields="{ text: 'theCodeName', value: 'siteCode' }"
            @change="siteOrgClick"
            :allow-filtering="true"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item v-if="dis" :label="$t('采购订单号')">
          <mt-input v-model="topInfo.orderCode" :disabled="dis || isDisabled2"></mt-input>
        </mt-form-item>
        <mt-form-item v-if="dis" :label="$t('采购组')">
          <mt-input v-model="topInfo.purchaseGroupName" :disabled="dis || isDisabled2"></mt-input>
        </mt-form-item>
        <mt-form-item v-if="dis" :label="$t('物料')">
          <mt-input v-model="topInfo.itemName" :disabled="dis || isDisabled2"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item v-if="dis" :label="$t('发料仓位')">
          <mt-input v-model="topInfo.warehouseName" :disabled="dis"></mt-input>
        </mt-form-item> -->
        <mt-form-item prop="warehouseCode" :label="$t('库存地点')">
          <mt-input v-if="dis" disabled type="text" v-model="topInfo.warehouseName"></mt-input>
          <mt-select
            v-else
            :show-clear-button="true"
            v-model="topInfo.warehouseCode"
            :open-dispatch-change="false"
            @change="warehouseClick"
            :allow-filtering="true"
            :data-source="cancelList"
            :fields="{ text: 'label', value: 'siteAddress' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('领料供应商')">
          <mt-input disabled type="text" v-model="topInfo.supplierCodeName"></mt-input>
        </mt-form-item>

        <mt-form-item :label="$t('是否销售委外')">
          <mt-radio
            v-model="topInfo.isOutSale"
            :data-source="radioData1"
            @change="radioChange"
            :disabled="isDisabled2"
          ></mt-radio>
        </mt-form-item>
        <mt-form-item
          v-if="topInfo.buyerApproveReaseon !== null && topInfo.buyerApproveReaseon !== undefined"
          prop="businessTypeId"
          :label="$t('采方退回原因')"
        >
          <mt-input
            v-model="topInfo.buyerApproveReaseon"
            :disabled="true"
            :placeholder="$t('采方退回原因')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input @change="radioChange" v-model="topInfo.remark" :disabled="dis"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
    <!-- 待领料明细创建 -->
    <div class="main-bottom" v-show="topInfo.createType === '1' && entryType == 'edit'">
      <mt-form
        ref="newRuleForm"
        :model="newRuleForm"
        :rules="newRuleFormRules"
        :validate-on-rule-change="false"
      >
        <mt-form-item :label="$t('客户公司')" prop="customerCode">
          <mt-select
            v-model="newRuleForm.customerCode"
            :allow-filtering="true"
            :disabled="isDisabled2"
            :filtering="getSourceCompany1"
            :fields="{ text: 'label', value: 'value' }"
            :data-source="customerCodeOptions"
            @change="companyChange2"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('工厂')" prop="siteCode">
          <mt-select
            :show-clear-button="false"
            v-model="newRuleForm.siteCode"
            :open-dispatch-change="false"
            :disabled="isDisabled2"
            :allow-filtering="true"
            @change="siteChange"
            :data-source="pickingSiteCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('需求日期')">
          <mt-date-range-picker
            :placeholder="$t('选择开始时间和结束时间')"
            v-model="newRuleForm.requireDate"
            :disabled="isDisabled2"
          ></mt-date-range-picker>
        </mt-form-item>
        <!-- <mt-form-item :label="$t('需求日期')">
          <mt-date-range-picker
            :placeholder="$t('选择开始时间和结束时间')"
            v-model="newRuleForm.requireDate"
          ></mt-date-range-picker>
        </mt-form-item> -->
        <!-- <mt-form-item :label="$t('需求日期')">
          <mt-date-range-picker
            :width="300"
            :placeholder="$t('选择开始时间和结束时间')"
            v-model="newRuleForm.requireDate"
          ></mt-date-range-picker>
        </mt-form-item> -->

        <mt-form-item prop="orderCode" :label="$t('采购订单号')">
          <mt-input type="text" :disabled="isDisabled2" v-model="newRuleForm.orderCode"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('采购组')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.purchaseGroupCode"
            :open-dispatch-change="false"
            :disabled="isDisabled2"
            :allow-filtering="true"
            :filtering="getPurchaseGroupCodeOptions"
            :data-source="purchaseGroupCodeOptions"
            :fields="{ text: 'label', value: 'groupCode' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCodeName" :label="$t('领料供应商')">
          <mt-input disabled type="text" v-model="newRuleForm.supplierCodeName"></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('物料')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.itemCode"
            :open-dispatch-change="false"
            :disabled="isDisabled2"
            :allow-filtering="true"
            :filtering="getItemCodeOptions"
            :data-source="itemCodeOptions"
            :fields="{ text: 'label', value: 'itemCode' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('库存地点')">
          <mt-select
            :show-clear-button="true"
            v-model="newRuleForm.warehouseCode"
            @change="newWarehouseClick"
            :open-dispatch-change="false"
            :allow-filtering="true"
            :data-source="cancelList"
            :fields="{ text: 'label', value: 'siteAddress' }"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="isOutSale" :label="$t('是否销售委外')">
          <mt-radio v-model="newRuleForm.isOutSale" disabled :data-source="radioData1"></mt-radio>
        </mt-form-item>
        <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
          <mt-input v-model="newRuleForm.remark"></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </div>
</template>

<script>
// import UTILS from "@/utils/utils";
import { Query } from '@syncfusion/ej2-data'
import { cloneDeep, isEqual } from 'lodash'
import { maxPageSize } from '@/utils/constant'

import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { addCodeNameKeyInList } from '@/utils/utils'
const siteCodeSelectTemplate = () => {
  return {
    template: Vue.component('siteCodeSelectTemplate', {
      template: `
    <div>
      <div>{{data.siteCode}}-{{data.siteName}}</div>
    </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
const buyCodeSelectTemplate = () => {
  return {
    template: Vue.component('buyCodeSelectTemplate', {
      template: `
        <div class="grid-edit-column mt-flex-direction-column">
            <div class="field-content">
              <span>{{data.customerCode}}+{{data.customerName}}</span>
            </div>
          </div>`,
      data() {
        return { data: {} }
      }
    })
  }
}
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  props: {
    entryType: {
      type: String,
      default: 'edit'
    },
    topInfoE: {
      type: Object,
      default: () => {
        return {
          // createType: "1",
          // isOutSale: 1,
        }
      }
    },
    entryNewRuleForm: {
      type: Object,
      default: () => {}
    },
    costSwitch: {
      type: String,
      default: '1'
    },
    entrySource: {
      type: String,
      default: '1'
    },
    acceptanceUse: {
      type: String,
      default: '0'
    },
    entryDraft: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      addId: '1',
      isDisabled2: false,
      itemCodeOptions: [],
      useingNewRuleForm: {},
      pickingSiteCodeOptions: [], //领料工厂下拉
      buyerOrgCode: '',
      buyerOrgName: '',
      purchaseGroupCodeOptions: [], //采购组下拉
      cancelList: [],
      customerCodeOptions: [], //客户公司下拉
      newRuleForm: {
        customerCode: '', //客户公司
        newRuleForm: '', // 采购订单号
        customerName: '', //客户公司名称
        enterpriseId: '', // 企业id
        requireDate: '', // 需求日期
        outsourcedSiteCode: '', // 委外工厂
        outsourcedSiteName: '', // 委外工厂名称
        supplierCodeName: '', // 领料供应商
        purchaseGroupCode: '', //采购组
        purchaseGroupName: '', //采购组名称
        orderCode: '', // 采购订单号
        orderItemNo: '', //采购订单行号
        itemCode: '', // 物料
        itemName: '', //物料名称
        warehouseCode: '', // 发料库存地点
        siteCode: '', // 领料工厂
        siteName: '', //领料工厂名称
        isOutSale: '0', //是否销售委外
        remark: '', // 备注
        supplierCode: '',
        supplierName: '',
        id: ''
      },
      newRuleFormRules: {
        customerCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        outsourcedSiteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        supplierCodeName: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      receiveSite: '',
      siteCodeValueTemplate: siteCodeSelectTemplate, // 工厂
      orderTypeOptions: [],

      buyerItem: buyCodeSelectTemplate,
      radioData: [
        {
          label: this.$t('待领料明细'),
          value: '1'
          // disabled: true,
        },
        {
          label: this.$t('手工创建'),
          value: '2'
        }
      ],

      addForm: {},
      radioData1: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      isExpand: true,
      rules: {
        siteId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        orderCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        isOutSale: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        warehouseCode: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        customerEnterpriseId: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },

      expedited: '1', //待领料明细or手工创建
      siteOptions: [],

      dataArr3: [],
      userInfo: JSON.parse(sessionStorage.getItem('userInfo')),
      topInfo: cloneDeep(this.topInfoE),
      buyerEnterpriseId: '', // 在本页面中都么有用到
      type: '',
      entryFirst: '1'
    }
  },
  computed: {
    dis() {
      return this.entryType === 'readonly'
    }
  },
  watch: {
    topInfoE: {
      handler() {
        this.topInfo = cloneDeep(this.topInfoE)
        if (this.topInfo.customerEnterpriseId) {
          if (this.topInfo.createType == '2' || this.entryType == 'readonly') {
            this.type = 'edit'

            this.buyerEnterpriseId = this.topInfo.customerEnterpriseId
            this.topInfo.supplierCodeName = this.topInfo.supplierCode + this.topInfo.supplierName
            this.receiveSite = this.topInfo.siteCode + this.topInfo.siteName
          }
        }
      },
      immediate: true
    },
    entryNewRuleForm: {
      handler(val) {
        if (this.entryFirst != '1') return
        if (this.entryType != 'edit') return
        if (val.createType == '1') {
          this.topInfo.createType = '1'
          this.entryFirst = '2'
          this.newRuleForm.customerCode = val.buyerOrgCode
          this.newRuleForm.customerName = val.buyerOrgName
          this.newRuleForm.enterpriseId = val.buyerEnterpriseId // 企业id
          if (val.demandStartDate && val.demandEndDate) {
            this.newRuleForm.requireDate = [
              new Date(Number(val.demandStartDate)),
              new Date(Number(val.demandEndDate))
            ]
          }
          this.newRuleForm.purchaseGroupCode = val.purchaseGroupCode
          this.newRuleForm.siteCode = val.siteCode
          this.newRuleForm.siteName = val.siteName
          this.getWarehouseCodeOptions(this.newRuleForm.siteCode)
          this.getItemCodeOptions({ text: val.itemCode }, val.siteCode)
          this.newRuleForm.itemCode = val.itemCode
          this.newRuleForm.warehouseCode = val.warehouseCode
          this.newRuleForm.itemName = val.itemName
          this.newRuleForm.orderCode = val.orderCode
          this.newRuleForm.supplierCode = val.supplierCode
          this.newRuleForm.supplierName = val.supplierName
          this.newRuleForm.supplierCodeName = `${val.supplierCode}-${val.supplierName}` // 领料供应商

          // this.getPurchaseGroupCodeOptions({ text: val.buyerOrgCode });

          this.newRuleForm.orderItemNo = val.orderItemNo //采购订单行号

          // this.newRuleForm.siteCode = val.receiveSiteCode; //领料工厂
          this.newRuleForm.pickingSiteName = val.receiveSiteName //领料工厂名称
          this.newRuleForm.isOutSale = '0'
          this.newRuleForm.remark = val.remark
          this.newRuleForm.id = val.id
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // this.topInfo = cloneDeep(this.topInfoE);
    // console.log("this.topInfo", this.topInfo);
    this.fuzzyQuery('')
  },
  mounted() {
    // this.getPurchaseGroupCodeOptions();
    // this.topInfo.isOutSale = "0";

    this.topInfo.supplierName = this.userInfo.enterpriseName
    this.topInfo.createUserName = this.userInfo.username
    this.topInfo.siteId = this.userInfo.siteId
  },
  methods: {
    init() {
      this.isDisabled2 = true
    },
    //库存地点
    warehouseClick(e) {
      this.topInfo.warehouseName = e.itemData.siteAddressName
    },
    getWarehouseCodeOptions(e) {
      let obj = {
        enterpriseId: this.newRuleForm.enterpriseId || this.topInfo.customerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: e
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.siteAddress + `-` + item.siteAddressName
        })
        this.cancelList = res.data.records
      })
      if (this.topInfoE.warehouseCode) {
        setTimeout(() => {
          this.topInfo.warehouseCode = this.topInfoE.warehouseCode
          this.topInfo.warehouseName = this.topInfoE.warehouseName
        }, 500)
      }
    },
    // 获取物料
    getItemCodeOptions(e, siteCode) {
      let params = {
        page: {
          current: 1,
          size: 10
        },
        customerEnterpriseId: this.newRuleForm.enterpriseId,
        organizationCode: siteCode ? siteCode : this.newRuleForm.siteCode,
        rules: [
          {
            field: 'itemCode',
            operator: 'contains',
            value: e.text ? e.text : ''
          }
          // {
          //   field: "organizationCode",
          //   operator: "equal",
          //   value: this.codeArr.siteCode,
          // },
        ]
      }
      this.$API.masterData.getOrgRel(params).then((res) => {
        res.data.records.forEach((item) => {
          item.label = item.itemCode + '-' + item.itemName
        })
        this.itemCodeOptions = res.data?.records || []
      })
    },
    //获取采购组
    getPurchaseGroupCodeOptions(val) {
      let params = {
        customerEnterpriseId: this.newRuleForm.enterpriseId,
        fuzzyParam: val.text
      }
      this.$API.masterData.businessGetGroup(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.groupCode} - ${item.groupName}`
        })
        this.purchaseGroupCodeOptions = list
        // if (val?.updateData) {
        //   this.$nextTick(() => {
        //     val.updateData(this.dataSource);
        //   });
        // }
      })
    },
    radioChange() {
      sessionStorage.setItem('codeArr', JSON.stringify(this.topInfo))
    },
    siteChange(e) {
      this.newRuleForm.siteName = e.itemData.siteName
      this.getItemCodeOptions({}, e.itemData.siteCode)
      this.getWarehouseCodeOptions(e.itemData.siteCode)
    },
    getPickingSiteCodeOptions(val) {
      //领料工厂
      let params = {
        paramObj: this.newRuleForm.organizationCode
        // enterpriseId: this.newRuleForm.enterpriseId,
        // fuzzyParam: val?.text || "",
        // dataLimit: 10000,
      }
      this.$API.masterData.getSiteListSupplier(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.siteName}-${item.siteCode}`
          item.value = item.siteCode
        })
        this.pickingSiteCodeOptions = list
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.pickingSiteCodeOptions)
          })
        }
      })
    },

    companyChange2(e) {
      //公司改变
      if (e.e !== null) {
        this.newRuleForm.siteCode = ''
        this.newRuleForm.siteName = ''
      }
      this.newRuleForm.buyerOrgCode = e.itemData?.customerCode
      this.newRuleForm.buyerOrgName = e.itemData?.customerName
      this.newRuleForm.customerName = e.itemData?.customerName
      this.newRuleForm.customerCode = e.itemData?.customerCode
      this.newRuleForm.enterpriseId = e.itemData?.customerEnterpriseId
      this.newRuleForm.organizationCode = e.itemData?.organizationCode
      this.findInBuyingByCustomerCode2(e.itemData?.customerCode)

      this.getPickingSiteCodeOptions() //领料工厂
      this.getPurchaseGroupCodeOptions({})
      // this.getPurchaseGroupCodeOptions(); //采购组
    },
    findInBuyingByCustomerCode2(customerCode) {
      //获取供应商信息
      this.$API.masterData
        .findInBuyingByCustomerCode({ customerCode: customerCode })
        .then((res) => {
          this.newRuleForm.supplierCodeName = `${res.data.supplierCode}-${res.data.supplierName}`
          this.newRuleForm.supplierCode = res.data.supplierCode
          this.newRuleForm.supplierName = res.data.supplierName
        })
    },
    getItemInfo() {
      //获取物料信息
      if (!this.newRuleForm.customerCode) {
        this.$toast({
          content: '请选择客户公司!',
          type: 'warning'
        })
        return
      }

      if (!this.newRuleForm.siteCode) {
        this.$toast({
          content: '请选择工厂!',
          type: 'warning'
        })
        return
      }
      this.$store.commit('startLoading')
      this.useingNewRuleForm = cloneDeep(this.newRuleForm)
      let obj = {
        buyerEnterpriseId: this.newRuleForm.enterpriseId,
        createType: this.topInfo.createType,
        buyerGroupCode: this.newRuleForm.purchaseGroupCode,
        isOutSale: this.newRuleForm.isOutSale,
        buyerOrgCode: this.newRuleForm.buyerOrgCode,
        orderCode: this.newRuleForm.orderCode,
        itemCode: this.newRuleForm.itemCode,
        siteCode: this.newRuleForm.siteCode,
        supplierCode: this.newRuleForm.supplierCode,
        warehouseCode: this.newRuleForm.warehouseCode,
        demandStartDate: this.newRuleForm.requireDate[0]?.getTime(),
        demandEndDate: this.newRuleForm.requireDate[1]?.getTime()
      }

      this.$API.outsourcing
        .outNewWaitReceiveOrderQuery(obj)
        .then((res) => {
          let list = res.data
          list.forEach((item) => {
            item.receiveQuantity = item.unReceiveNum
            this.$store.commit('endLoading')
            // item.maxReceiveQuantity = item.maxReceiveQuantity;
            // item.buyerOrgCode = item.purchaseGroupCode;
            // item.buyerOrgName = item.purchaseGroupName;
            // item.packageMinQuantity = item.minPackNum;
            item.remark = ''
            item.addId = this.addId++
            delete item.id
          })
          this.$store.commit('endLoading')

          this.$emit('updateDataSource', list)
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    newSave() {
      //待领料明细保存
      this.$refs.newRuleForm.validate((valid) => {
        if (valid) {
          let flag = isEqual(this.useingNewRuleForm, this.newRuleForm)
          if (!flag) {
            this.$toast({
              content: this.$t('请重新获取物料信息'),
              type: 'warning'
            })
            return
          }

          // this.newRuleForm.pickingSiteName = this.pickingSiteCodeOptions.find(
          //   (item) => {
          //     return item.siteCode == this.newRuleForm.pickingSiteCode;
          //   }
          // )?.siteName;

          this.$emit('newSave', this.newRuleForm)
        }
      })
    },
    newSubmit() {
      //待领料明细提交
      this.$refs.newRuleForm.validate((valid) => {
        if (valid) {
          let flag = isEqual(this.useingNewRuleForm, this.newRuleForm)
          if (!flag) {
            this.$toast({
              content: this.$t('请重新获取物料信息'),
              type: 'warning'
            })
            return
          }

          // this.newRuleForm.pickingSiteName = this.pickingSiteCodeOptions.find(
          //   (item) => {
          //     return item.siteCode == this.newRuleForm.pickingSiteCode;
          //   }
          // )?.siteName;

          this.$emit('newSubmit', this.newRuleForm)
        }
      })
    },
    getSourceCompany1(e) {
      var searchData = this.dataArr3

      // load overall data when search key empty.
      if (e.text == '') e.updateData(searchData)
      else {
        let query = new Query().select([
          'name',
          'code',
          'customerCode',
          'customerName',
          'customerEnterpriseId'
        ])
        // change the type of filtering
        query = e.text !== '' ? query.where('name', 'contains', e.text, true) : query
        console.log(query)
        e.updateData(searchData, query)
        console.log(searchData)
      }
    },
    siteOrgClick(e) {
      console.log(e.itemData)
      if (e.e !== null) {
        this.topInfo.warehouseCode = ''
        this.topInfo.warehouseName = ''
      }

      this.topInfo.buyerOrgId = e.itemData.organizationId
      this.topInfo.siteCode = e.itemData.siteCode
      this.topInfo.siteName = e.itemData.siteName
      this.getWarehouseCodeOptions(e.itemData.siteCode)

      // this.$nextTick(() => {
      //   this.$refs.rules.clearValidate(["siteCode"]);
      // });

      this.radioChange()
    },
    newWarehouseClick(e) {
      this.newRuleForm.warehouseName = e.itemData.siteAddressName
    },
    dateFormat(value) {
      let str = ''
      // 数据库时间戳默认值为 0，为 0 时不显示
      if (value == 0) {
        return str
      }
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }
      return str
    },

    typeInfo(e) {
      if (e === 0) {
        return this.$t('新建')
      } else if (e === 1) {
        return this.$t('待确认')
      } else if (e === 2) {
        return this.$t('退回')
      } else if (e === 3) {
        return this.$t('已确认')
      } else if (e === 4) {
        return this.$t('已完结')
      } else if (e === 5) {
        return this.$t('已完结')
      }
    },
    findInBuyingByCustomerCode() {
      this.$API.masterData
        .findInBuyingByCustomerCode({ customerCode: this.topInfo.buyerCode })
        .then((r) => {
          this.topInfo.supplierCodeName = `${r.data.supplierCode}-${r.data.supplierName}`
          this.topInfo.supplierName = r.data.supplierName

          this.topInfo.supplierCode = r.data.supplierCode

          this.$forceUpdate()
        })
    },

    companyChange(e) {
      if (e.e !== null) {
        this.topInfo.siteCode = ''
        this.topInfo.siteName = ''

        this.topInfo.warehouseCode = ''
        this.topInfo.warehouseName = ''
        this.cancelList = []
      }

      // this.topInfo.buyerOrgCode = "";
      console.log(e)
      if (this.topInfo.customerEnterpriseId) {
        this.$emit('deleteRe')
      }

      this.siteOptions = []
      this.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.topInfo.buyerEnterpriseId = e.itemData.customerEnterpriseId
      this.topInfo.buyerCode = e.itemData.customerCode
      this.topInfo.buyerName = e.itemData.customerName
      this.topInfo.buyerOrgCode = e.itemData.customerCode
      this.topInfo.buyerOrgName = e.itemData.customerName
      this.topInfo.organizationCode = e.itemData?.organizationCode
      this.radioChange()
      this.getSourceAvailable({})
      this.findInBuyingByCustomerCode()
      // this.$nextTick(() => {
      this.$refs['rules'].clearValidate(['siteCode'])
      // });
    },
    submit() {
      console.log(this.topInfo)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.topInfo)

          this.$emit('submit', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    fuzzyQuery() {
      this.$API.outsourcing.customerQuery({ fuzzyNameOrCode: '' }).then((res) => {
        res.data.forEach((item) => {
          item.name = item.organizationCode + '-' + item.customerName
          item.code = item.customerEnterpriseId
          item.label = item.organizationCode + '-' + item.customerName
          item.value = item.customerCode
        })
        this.dataArr3 = res.data
        this.customerCodeOptions = res.data
      })
    },

    getSourceAvailable(args) {
      const { updateData } = args
      let obj = {
        paramObj: this.topInfo.organizationCode
      }
      this.$API.masterData.getSiteListSupplier(obj).then((res) => {
        const list = res?.data || []
        this.siteOptions = addCodeNameKeyInList({
          firstKey: 'siteCode',
          secondKey: 'siteName',
          list
        })
        if (updateData) {
          this.$nextTick(() => {
            updateData(this.siteOptions)
          })
        }
      })
    },
    goBack() {
      // this.$router.push(`new-supplier-picking`);
      this.$router.go(-1)
    },
    save() {
      console.log('this.topInfo', this.topInfo)
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.topInfo)

          this.$emit('save', this.topInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    expandChange() {
      this.isExpand = !this.isExpand
      // this.$refs.ruleForm.clearValidate();
    },
    onchang(e) {
      this.$emit('onchang', e)
      // console.log("onchang", e, this.expedited);
    },

    // 获取其他数据 code、name
    getOtherInfo(params, refName, value) {
      // 业务类型、申请人、公司、申请部门、采购组织
      if (value && this.$refs[refName]) {
        let _data = this.$refs[refName].ejsRef.getDataByValue(value)
        // console.log("params", _data);
        for (let key in _data) {
          params[key] = _data[key]
        }
        if (!_data) return
      }
      console.log('params', params)
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin: 20px 20px 20px 0;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itemcon {
      display: flex;
      align-items: center;
    }
    .item {
      margin-right: 20px;
    }
    .middle-blank {
      flex: 1;
    }
    .status {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      // &.more-width {
      //   // width: 450px;
      // }

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
</style>
