<template>
  <div class="full-height pt20 vertical-flex-box">
    <top-info
      @submitClick="submitClick"
      @resolve="resolve"
      @deleteRe="deleteRe"
      :header-info="headerInfo"
      ref="topInfo"
      class="flex-keep"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :load="load"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      @selectedChanged="selectedChanged"
    >
    </mt-template-page>
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :is-show-tips="true"
      :request-urls="requestUrls"
      @closeUploadExcel="handleImport(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
    <!-- </div> -->
  </div>
</template>
<script>
// import { editSettings } from './config/index.js'
import { pageConfig, editSettings } from './config/createReturnCfg'
import { cloneDeep } from 'lodash'

export default {
  components: {
    TopInfo: () => import('./components/topInfo'),
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      headerInfo: {
        siteCode: '',
        remark: ''
      },
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数

      requestUrls: {
        templateUrlPre: 'outsourcing',
        templateUrl: 'outReceiveOrderTemplate', // 下载模板接口方法名
        uploadUrl: 'outCancelOrderImport' // 上传接口方法名
      },
      beginData: null,
      editSettings,
      pageConfig: pageConfig,
      rowList: [],
      topList: {},
      entryId: '', //编辑带入的订单id
      entryType: null, //1是新增 2是编辑 3是反馈异常
      entrySource: '', // 0采购申请 1手工创建 2商城进入 4合同进入
      entryDraft: '', //1是草稿 2不是草稿 草稿可以修改
      ids: '',
      saveList: null,
      topInfo: {
        orderCode: '',
        createType: '1',
        isOutSale: '0'
      },
      selectedOtherInfo: {},

      edit: ''
    }
  },
  mounted() {
    this.edit = this.$route.query.edit
    if (this.edit) {
      this.getOnePicking()
    }
    window.pickGrid = this.$refs.templateRef
  },
  methods: {
    handleImport(flag) {
      //导入
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    upExcelConfirm(e) {
      this.handleImport(false)
      e.data.forEach((item) => {
        item.cancelQuantity = item.quantity
        item.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
      })
      this.$refs.templateRef.$refs[
        'pageGridRef-0'
      ][0].$refs.pluginGridRef.$refs.gridRef.ejsInstances.dataSource.push(...e.data)

      this.$refs.templateRef.getCurrentTabRef()?.grid.refresh()
      // this.pageConfig[0].grid.dataSource = e.data;
    },
    load(args) {
      console.log('load-----------', args)
      this.$refs.grid.$el.addEventListener('keydown', this.keyDownHandler)
    },
    keyPressed(args) {
      console.log('keyPressed', args)
    },
    keyDownHandler(e) {
      console.log('keyDownHandler', e)
    },
    // 保存操作
    submitClick(val) {
      console.log('val')
      console.log(val)
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []

      this.rowList = currentRecords
      let obj = {
        outCancelOrderDetailRequestList: this.rowList,
        ...val,
        orderType: this.$route.path.includes('gc') ? 1 : 0
      }
      if (
        obj.isOutSale === '1' &&
        obj.isOutDirect === '1' &&
        (obj.materialSupplierCode === '' ||
          obj.materialSupplierCode === null ||
          obj.materialSupplierCode === undefined)
      ) {
        this.$toast({
          content: this.$t('原材料供应商必填'),
          type: 'warning'
        })
        return
      }
      if (this.ids.length > 0) {
        obj.id = this.ids
      }
      const _flag = this.validItemData(obj.outCancelOrderDetailRequestList)
      if (!_flag) {
        this.$toast({
          content: this.$t('一张退货单只能创建一个采购组的明细'),
          type: 'warning'
        })
        return
      }

      this.$API.outsourcing.createReturnBillSave(obj).then((r) => {
        if (r.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          this.ids = r.data
          // this.$router.go(-1)
        }
      })
    },
    // 提交操作
    resolve(val) {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []

      this.rowList = currentRecords
      if (this.ids.length > 0) {
        let obj = [this.ids]
        this.$API.outsourcing.createReturnBillSubmit(obj).then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
          }
        })
      } else {
        let obj = {
          outCancelOrderDetailRequestList: this.rowList,
          ...val,
          orderType: this.$route.path.includes('gc') ? 1 : 0
        }
        const _flag = this.validItemData(obj.outCancelOrderDetailRequestList)
        if (!_flag) {
          this.$toast({
            content: this.$t('一张退货单只能创建一个采购组的明细'),
            type: 'warning'
          })
          return
        }

        if (
          obj.isOutSale === '1' &&
          obj.isOutDirect === '1' &&
          (obj.materialSupplierCode === '' ||
            obj.materialSupplierCode === null ||
            obj.materialSupplierCode === undefined)
        ) {
          this.$toast({
            content: this.$t('原材料供应商必填'),
            type: 'warning'
          })
          return
        }
        obj.outCancelOrderDetailRequestList.forEach((item) => {
          return (item.orderCode = val.orderCode), (item.orderItemNo = val.orderItemNo)
        })
        if (this.ids.length > 0) {
          obj.id = this.ids
        }
        this.$API.outsourcing.createReturnBillSave(obj).then((r) => {
          this.$API.outsourcing.createReturnBillSubmit([r.data]).then(() => {
            // if (res.code === 200) {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.go(-1)
            // }
          })
        })
      }
    },
    // 校验物料信息数据
    validItemData(data) {
      if (!data || data.length <= 0) return true
      const res = new Map()
      const _data = data.filter(
        (item) => !res.has(item.buyerGroupCode) && res.set(item.buyerGroupCode, 1)
      )
      if (_data.length !== 1) return false
      return true
    },
    selectedChanged(val) {
      console.log(val)
      console.log(this.selectedOtherInfo)
      Object.assign(this.selectedOtherInfo, val.itemInfo)
      this.saveList = this.selectedOtherInfo
    },
    getOnePicking() {
      this.$store.commit('startLoading')

      this.$API.outsourcing
        .supplierNewGetOne({ cancelOrderCode: this.edit })
        .then((r) => {
          // this.topInfo.siteId = r.data.siteId;
          // this.topInfo.supplierCode = r.data.supplierCode;
          // this.topInfo.orderCode = r.data.orderCode;
          // this.topInfo.isOutSale = r.data.isOutSale;
          // this.topInfo.remark = r.data.remark;
          sessionStorage.setItem('order', JSON.stringify(r.data))

          this.headerInfo = cloneDeep(r.data) || {}
          this.$refs.topInfo.init()

          this.topList.siteCode = r.data.siteCode
          console.log(this.headerInfo, '321321')
          this.pageConfig[0].grid.dataSource = r.data.outCancelOrderDetailResponseList
          this.$store.commit('endLoading')
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },

    deleteRe() {
      // this.$dialog({
      //   data: {
      //     title: this.$t("确认"),
      //     message: this.$t(
      //       "更换公司和采购订单将重新清空/获取领料物料数据，请确定是否继续?"
      //     ),
      //   },
      // success: () => {
      let currentRecords =
        this.$refs.templateRef?.getCurrentUsefulRef().gridRef?.ejsRef.getCurrentViewRecords() || []
      if (currentRecords.length > 0) {
        let numList = []
        for (let i = 0; i < currentRecords.length; i++) {
          numList.push(i)
        }
        console.log(numList)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRows(numList)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
      //   },
      // });
    },
    handleClickToolBar(e) {
      console.log('handleClickToolBar', e)
      let codeArr = JSON.parse(sessionStorage.getItem('codeArr'))

      if (e.toolbar.id === 'Add') {
        if (
          codeArr.siteCode === undefined ||
          codeArr.siteCode === null ||
          codeArr.siteCode === ''
        ) {
          this.$toast({
            content: this.$t('头部带星号的为必填项'),
            type: 'warning'
          })
        } else if (
          codeArr.isOutSale === undefined ||
          codeArr.isOutSale === null ||
          codeArr.isOutSale === '' ||
          codeArr.isOutDirect === undefined ||
          codeArr.isOutDirect === null ||
          codeArr.isOutDirect === ''
        ) {
          this.$toast({
            content: this.$t('是否销售委外和是否直退材料商为必填'),
            type: 'warning'
          })
        } else if (
          codeArr.isOutDirect === '0' &&
          (codeArr.warehouseCode === undefined ||
            codeArr.warehouseCode === null ||
            codeArr.warehouseCode === '')
        ) {
          this.$toast({
            content: this.$t('请填写库存地点'),
            type: 'warning'
          })
        } else if (
          codeArr.isOutDirect === '1' &&
          (codeArr.materialSupplierCode === undefined ||
            codeArr.materialSupplierCode === null ||
            codeArr.materialSupplierCode === '')
        ) {
          this.$toast({
            content: this.$t('请填写原材料供应商'),
            type: 'warning'
          })
        } else {
          console.log(this.topList)
          console.log('ref', this.$refs.templateRef.getCurrentUsefulRef())
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
          this.$refs.topInfo.init()
        }
      } else if (e.toolbar.id === 'Import') {
        if (
          codeArr.siteCode === undefined ||
          codeArr.siteCode === null ||
          codeArr.siteCode === ''
        ) {
          this.$toast({
            content: this.$t('头部带星号的为必填项'),
            type: 'warning'
          })
        } else if (
          codeArr.isOutSale === undefined ||
          codeArr.isOutSale === null ||
          codeArr.isOutSale === '' ||
          codeArr.isOutDirect === undefined ||
          codeArr.isOutDirect === null ||
          codeArr.isOutDirect === ''
        ) {
          this.$toast({
            content: this.$t('是否销售委外和是否直退材料商为必填'),
            type: 'warning'
          })
        } else if (
          codeArr.isOutDirect === '0' &&
          (codeArr.warehouseCode === undefined ||
            codeArr.warehouseCode === null ||
            codeArr.warehouseCode === '')
        ) {
          this.$toast({
            content: this.$t('请填写库存地点'),
            type: 'warning'
          })
        } else if (
          codeArr.isOutDirect === '1' &&
          (codeArr.materialSupplierCode === undefined ||
            codeArr.materialSupplierCode === null ||
            codeArr.materialSupplierCode === '')
        ) {
          this.$toast({
            content: this.$t('请填写原材料供应商'),
            type: 'warning'
          })
        } else {
          let obj = {
            buyerEnterpriseId: codeArr.buyerEnterpriseId,
            createType: 2,
            siteCode: codeArr.siteCode,
            buyerOrgCode: codeArr.buyerOrgCode,
            isOutSale: codeArr.isOutSale,
            supplierCode: codeArr.supplierCode
          }
          this.uploadParams = obj
          this.handleImport(true)
          return
        }
      } else if (e.toolbar.id === 'Delete') {
        console.log(this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef)
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    },

    handleClickCellTool(e) {
      console.log('handleClickCellTool', e)
    },
    handleClickCellTitle(e) {
      console.log('handleClickCellTitle', e)
    },
    actionComplete() {},
    actionBegin(args) {
      console.log('actionBegin', args)
      // const { index } = args;
      if (args.requestType === 'sorting') {
        // if (this.allowSubmit == false) {
        args.cancel = true
        // }
      }
      if (args.requestType === 'save') {
        if (args.data.itemCode === null && this.beginData !== null) {
          args.data.itemCode = this.beginData.itemCode
        }
        if (this.saveList !== null) {
          console.log(this.saveList)
          args.data.warehouseCode = this.saveList.warehouseCode
          args.data.warehouseName = this.saveList.warehouseName
          args.data.buyerGroupName = this.saveList.buyerGroupName
          args.data.buyerGroupCode = this.saveList.buyerGroupCode
          args.data.basicUnitCode = this.saveList.basicUnitCode
          args.data.basicUnitName = this.saveList.basicUnitName

          console.log(args.data)
        }
      }

      if (args.requestType == 'add' || args.requestType == 'beginEdit') {
        this.beginData = args.rowData

        args.data.addId = 'add' + Math.random().toString(36).substr(3, 8) // 新增时是addId，后台获取过来的数据是id
        this.isEditStatus = true
        // this.nowEditRowFlag = args.data.addId;

        console.log(args.data)
      }
      if (args.requestType == 'refresh') {
        this.isEditStatus = false
      }
    },

    addRow(row) {
      console.log('addRow', row)
    }
  },
  deactivated() {
    this.pageConfig[0].grid.dataSource = []
    sessionStorage.removeItem('order')
    sessionStorage.removeItem('codeArr')
  }
}
</script>
<style lang="scss" scoped>
// /deep/ .top-info {
//   margin-top: 20px;
// }
.bottom-tables {
  height: 100%;
}
// .addPicking {
//   background: #fff;
//   display: flex;
//   flex-direction: column;
//   .template-height {
//     height: auto;
//     flex: 1;
//     max-height: calc(100% - 270px);
//     /deep/ .e-gridcontent {
//       height: 100%;
//     }
//     /deep/ .e-gridcontent {
//       height: 100%;
//     }
//   }
// }
</style>
