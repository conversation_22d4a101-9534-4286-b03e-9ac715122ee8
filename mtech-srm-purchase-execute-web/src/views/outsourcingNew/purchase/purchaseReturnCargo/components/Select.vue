<template>
  <div>
    <div class="in-cell">
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="postChange"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'value' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>
    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
// import { utils } from "@mtech-common/utils";
import { maxPageSize } from '@/utils/constant'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '3aee5144-1201-4eb5-8a0d-8b5792261a9a',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
              // {
              //   width: "150",
              //   field: "itemUnitDescription",
              //   headerText: this.$t("单位"),
              // },
              // {
              //   width: "150",
              //   field: "purchaseGroupName",
              //   headerText: this.$t("采购组"),
              // },
              // {
              //   width: "150",
              //   field: "batchCode",
              //   headerText: this.$t("批次号"),
              // },
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      codeArr: JSON.parse(sessionStorage.getItem('codeArr'))
    }
  },
  mounted() {
    // if (this.data.column.field === "itemCode") {
    //   //物料下拉
    //   this.getCategoryItem("");
    // }
    if (this.data.column.field === 'itemCode') {
      this.data.itemCode === null
        ? this.getCategoryItem('')
        : this.getCategoryItem(this.data.itemCode)

      this.getCategoryItem(this.data.itemCode)
    }
    if (this.data.column.field === 'warehouseCode') {
      this.getWarehouseCodeOptions()
    }
  },
  methods: {
    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(e, records) {
      if (!records || records.length <= 0) {
        records = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      }
      if (records.length <= 0) return
      this.selectChange({ itemData: records[0] })
    },
    showDialog() {
      this.pageConfig[0].grid.asyncConfig = {
        url: `masterDataManagement/auth/item-org-rel/pur-paged-query`,
        recordsPosition: 'data.records',
        condition: 'and',
        page: { current: 1, size: 20 },
        defaultRules: [
          {
            label: this.$t('物料编号'),
            field: 'itemCode',
            type: 'string',
            operator: 'contains',
            value: ''
          }
        ],
        organizationCode: this.codeArr?.siteCode ?? ''
      }
      this.$refs.dialog.ejsRef.show()
    },
    // 获取库存地点
    getWarehouseCodeOptions() {
      let obj = {
        enterpriseId: this.codeArr.buyerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.codeArr.siteCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.siteAddressName //
          item.value = item.siteAddress //
        })
        this.dataSource = res.data.records.map((i) => {
          return {
            ...i,
            codeAndName: `${i.siteAddress} - ${i.siteAddressName}`
          }
        })
      })
    },
    // 模糊
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      }
      // let obj = {
      //   currentPage: 1,
      //   keyword: "",
      //   pageSize: 20,
      // };
      // obj.keyword = e.text;
      // this.$API.masterData.getItemByKeyword(obj).then((res) => {
      //   res.data.records.forEach((item) => {
      //     item.name = item.itemCode; // 	客户名称
      //     item.value = item.itemCode; // 	客户名称
      //   });
      //   this.dataSource = res.data.records || [];
      // });
    },

    // getCategoryItem(e) {
    //   //物料下拉
    //   let obj = {
    //     page: {
    //       current: 1,
    //       size: 20,
    //     },
    //     rules: [
    //       {
    //         field: "itemCode",
    //         operator: "likeright",
    //         value: e,
    //       },
    //     ],
    //     customerEnterpriseId: this.codeArr.buyerEnterpriseId,
    //     organizationCode: this.codeArr.siteCode,
    //   };
    //   this.$API.masterData.getOrgRel(obj).then((res) => {
    //     res.data.records.forEach((item) => {
    //       item.name = item.itemCode; // 	客户名称
    //       item.value = item.itemCode; // 	客户名称
    //     });
    //     this.dataSource =
    //       res.data.records.map((i) => {
    //         return {
    //           ...i,
    //           codeAndName: `${i.itemCode} - ${i.itemName}`,
    //         };
    //       }) || [];
    //   });
    // },
    getCategoryItem(e) {
      //物料下拉
      let obj = {
        page: {
          current: 1,
          size: 100
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        organizationCode: this.codeArr?.siteCode ?? ''
      }
      this.$API.outsourcingNew.getitemCodeList(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.itemCode // 	客户名称
          item.value = item.itemCode // 	客户名称
        })
        this.dataSource =
          res.data.records.map((i) => {
            return {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`
            }
          }) || []
      })
    },
    selectChange(val) {
      console.log(val.itemData, '下拉数据的信息')
      if (this.data.column.field === 'itemCode') {
        this.data[this.data.column.field] = val.itemData?.itemCode
        this.postChange({ text: val.itemData?.itemCode })
        let obj = {
          buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
          createType: 2,
          itemCode: val.itemData.itemCode,
          buyerOrgCode: this.codeArr.buyerOrgCode,
          isOutSale: this.codeArr.isOutSale,
          id: this.data?.id,
          siteCode: this.codeArr.siteCode,
          supplierCode: this.codeArr.supplierCode
        }
        this.$bus.$emit('itemNameChange', val.itemData.itemName) //传给物料名称
        this.$API.outsourcingNew.OutQuerySapOutDemand(obj).then((res) => {
          this.$bus.$emit('planGroupNameChange', res.data[0].planGroupName) // 计划组
          this.$bus.$emit('buyerOrgNameChange', {
            buyerGroupName: res.data[0].buyerGroupName,
            buyerGroupCode: res.data[0].buyerGroupCode
          }) // 采购组
          // this.$bus.$emit("stockUnitCodeChange", res.data[0].stockUnitCode); //传给单位

          this.$bus.$emit('basicUnitCodeChange', {
            basicUnitCode: res.data[0].basicUnitCode,
            basicUnitName: res.data[0].basicUnitName
          }) //传给单位
          this.$bus.$emit('maxDemandQuantityChange', res.data[0].maxReceiveQuantity) //传给可调拨数量
          this.$bus.$emit('supplierStockChange', res.data[0].supplierStock) //传给库存现有
        })
        this.handleClose()
      }

      if (this.data.column.field === 'warehouseCode') {
        this.data.warehouseName = val.itemData.siteAddressName
        this.$parent.$emit('selectedChanged', {
          //传出额外数据
          fieldCode: 'warehouseCode',
          itemInfo: {
            ...this.data
          }
        })
        this.handleClose()
      }
    }
  },
  deactivated() {
    this.$bus.$off('maxDemandQuantityChange')
    this.$bus.$off('warehouseChange')
    this.$bus.$off('itemNameChange')
    this.$bus.$off('warehouseChange2')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
