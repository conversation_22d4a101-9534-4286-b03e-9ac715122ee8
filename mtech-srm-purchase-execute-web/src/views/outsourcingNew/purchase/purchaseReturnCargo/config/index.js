import { timeNumberToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号'),
    cellTools: []
  },
  {
    width: '125',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    },
    cellTools: [
      // {
      //   id: "Proper",
      //   icon: "",
      //   title: i18n.t("确认"),
      //   // permission: ["O_02_0126"],
      //   visibleCondition: (data) => data["status"] === 1,
      // },
      {
        id: 'cancel',
        title: i18n.t('取消'),
        visibleCondition: (data) => data['status'] == 5 && data['isOutDirect'] == 0
        // permission: ["O_02_1287"],
      },
      {
        id: 'restart',
        icon: 'icon_table_restart',
        title: i18n.t('手工同步'),
        // permission: ["O_02_0688"],
        visibleCondition: (data) => data['status'] === 5
      }
    ]
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '95',
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '95',
    field: 'syncStatus',
    headerText: i18n.t('同步状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('未同步'), cssClass: '' },
        { value: '1', text: i18n.t('同步成功'), cssClass: '' },
        { value: '2', text: i18n.t('同步失败'), cssClass: '' },
        { value: '3', text: i18n.t('同步中'), cssClass: '' }
      ]
    }
  },
  {
    field: 'syncMsg',
    headerText: i18n.t('同步消息')
  },
  {
    width: '110',
    field: 'materialApproveUserName',
    headerText: i18n.t('供方确认人')
  },
  {
    width: '125',
    field: 'materialApproveDate',
    headerText: i18n.t('供方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerApproveUserName',
    headerText: i18n.t('采方确认人')
  },
  {
    width: '125',
    field: 'buyerApproveDate',
    headerText: i18n.t('采方确认时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    width: '110',
    field: 'buyerCancelUserName',
    headerText: i18n.t('取消人')
  },
  {
    width: '125',
    field: 'buyerCancelDate',
    headerText: i18n.t('取消时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && !isNaN(e) && e.length == 13) {
          e = Number(e)
          return timeNumberToDate({
            formatString: 'YYYY-mm-dd',
            value: e
          })
        } else {
          return ''
        }
      }
    }
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂'),
    width: '265',
    searchOptions: MasterDataSelect.factoryAddress,

    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    width: '230',
    allowEditing: false,
    searchOptions: MasterDataSelect.businessCompany,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.materialSupplierCode}}-{{data.materialSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '170',
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  },
  {
    width: '95',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  },

  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号') //暂时没有
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '95',
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '105',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '65',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'wmsCancelQuantity',
    headerText: i18n.t('实退数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
        <div>
          <mt-input
          id="wmsCancelQuantity"
          v-model="data.wmsCancelQuantity"
          @change="change"
        ></mt-input>
        </div>
          `,
          data: function () {
            return {
              data: {}
            }
          },
          mounted() {},
          methods: {
            change(e) {
              this.data.wmsCancelQuantity = e
              this.$parent.$emit(`cellEdit`, {
                data: this.data,
                type: 'text',
                status: true
              })
            }
          }
        })
      }
    }
  },
  {
    width: '510',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: MasterDataSelect.material,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.itemCode}}-{{data.itemName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "itemName",
  //   headerText: i18n.t("物料名称"),
  // },
  {
    width: '95',
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '85',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,

    width: '255',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,

    width: '230',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '245',
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.materialSupplierCode}}-{{data.materialSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组'),
    searchOptions: MasterDataSelect.businessGroup,
    template: () => {
      return {
        template: Vue.component('buyerGroup', {
          template: `<div>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '170',
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  }
]
export const columnData3 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号') //暂时没有
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '95',
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '105',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '65',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'wmsCancelQuantity',
    headerText: i18n.t('实退数量')
  },
  {
    width: '510',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: MasterDataSelect.material,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.itemCode}}-{{data.itemName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "itemName",
  //   headerText: i18n.t("物料名称"),
  // },
  {
    width: '95',
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '85',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,

    width: '255',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,

    width: '230',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '245',
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.materialSupplierCode}}-{{data.materialSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组'),
    searchOptions: MasterDataSelect.businessGroup,
    template: () => {
      return {
        template: Vue.component('buyerGroup', {
          template: `<div>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '170',
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  }
]
export const columnData4 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '155',
    field: 'cancelOrderCode',
    headerText: i18n.t('委外退货单号') //暂时没有
  },
  {
    width: '95',
    field: 'isOutSale',
    headerText: i18n.t('销售委外'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '95',
    field: 'isOutDirect',
    headerText: i18n.t('委外直退'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('是'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '105',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
        { value: 2, text: i18n.t('供应商退回'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('供应商确认'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('采购退回'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('采购确认'), cssClass: 'col-inactive' },
        { value: 6, text: i18n.t('已完结'), cssClass: 'col-inactive' },
        { value: 7, text: i18n.t('已取消'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '65',
    field: 'lineNo',
    headerText: i18n.t('行号')
  },
  {
    width: '150',
    field: 'wmsCancelQuantity',
    headerText: i18n.t('实退数量')
  },
  {
    width: '510',
    field: 'itemCode',
    headerText: i18n.t('物料编码'),
    searchOptions: MasterDataSelect.material,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.itemCode}}-{{data.itemName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  // {
  //   width: "150",
  //   field: "itemName",
  //   headerText: i18n.t("物料名称"),
  // },
  {
    width: '95',
    field: 'cancelQuantity',
    headerText: i18n.t('退货数量')
  },
  {
    width: '65',
    field: 'basicUnitName',
    headerText: i18n.t('单位'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.basicUnitCode}}-{{data.basicUnitName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '85',
    field: 'remark',
    headerText: i18n.t('行备注')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂'),
    searchOptions: MasterDataSelect.factoryAddress,

    width: '255',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.siteCode}}-{{data.siteName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    field: 'buyerOrgCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,

    width: '230',
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.buyerOrgCode}}-{{data.buyerOrgName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '245',
    field: 'supplierCode',
    headerText: i18n.t('加工供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.supplierCode}}-{{data.supplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'materialSupplierCode',
    headerText: i18n.t('原材料供供应商'),
    searchOptions: MasterDataSelect.supplier,

    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.materialSupplierCode}}-{{data.materialSupplierName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '250',
    field: 'buyerGroupCode',
    headerText: i18n.t('采购组'),
    searchOptions: MasterDataSelect.businessGroup,
    template: () => {
      return {
        template: Vue.component('buyerGroup', {
          template: `<div>{{data.buyerGroupCode}}-{{data.buyerGroupName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '170',
    field: 'cancelAddress',
    headerText: i18n.t('退货地址')
  }
]
