<template>
  <!-- 发票协同（采方）列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 发票关联单据明细行弹框 -->
    <bill-details-table-dialog ref="billDetailsTableDialog"></bill-details-table-dialog>
    <!-- 发票回退弹框 -->
    <send-back-dialog ref="sendBackDialog" @confirm="sendBackDialogConfirm"></send-back-dialog>
    <!-- 退回sap -->
    <BackToSapDialog ref="backToSapDialog" @confirm="backToSapDialogConfirm"></BackToSapDialog>
  </div>
</template>

<script>
import { ColumnData, InvoiceStatus, Toolbar, SyncStatus, Table, TabCode } from './config/constant'
import { ConstantType } from '../detailV2/config/constant'
import { formatTableColumnData, serializeList } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import BillDetailsTableDialog from '@/components/businessComponents/billDetailsTableDialog'
import { BillDetailsTableDialogActionType } from '@/components/businessComponents/billDetailsTableDialog/config/constant'
import SendBackDialog from './components/sendBackDialog.vue'
import BackToSapDialog from './components/backToSapDialog.vue'

export default {
  components: {
    UploaderDialog,
    BillDetailsTableDialog,
    SendBackDialog,
    BackToSapDialog
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: Toolbar(localStorage.getItem('currentBu')),
          gridId: this.$tableUUID.invoiceCollaboration.summaryV2.list,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              table: Table.summary,
              data: ColumnData
            }),
            dataSource: [],
            // 租户级-发票协同-采方-采方采购-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/customer-reconciliation-invoice-v2/customer-purchaser-paged-query-role?BU_CODE=${localStorage.getItem(
                'currentBu'
              )}`,
              defaultRules: [],
              serializeList
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (e.toolbar.id === 'PushReimbursement') {
        // 推送报销
        this.handlePushReimbursement({ selectedRecords, idList })
      } else if (e.toolbar.id === 'PushPreMakeInvoice') {
        // 推送预制发票
        this.handlePushPreMakeInvoice({ selectedRecords, idList })
      } else if (e.toolbar.id === 'ApplyReturnToSap') {
        // 申请退回sap
        this.handleApplyReturnToSap({ selectedRecords, idList })
      }
    },
    // 推送报销
    handlePushReimbursement(args) {
      const { selectedRecords, idList } = args
      const { valid, invoiceStatus, syncStatus } = this.verifyInvoiceStatus({
        data: selectedRecords,
        checkStatus: true,
        checkSyncStatus: true
      })
      if (!valid || invoiceStatus != InvoiceStatus.complete || syncStatus != SyncStatus.notSynced) {
        this.$toast({
          content: this.$t('请选择财务已确认且未同步的数据'),
          type: 'warning'
        })
      } else {
        // 状态: 财务已确认 && 同步状态: 未同步
        this.postCustomerReconciliationInvoiceV2PushSharedFinance(idList)
      }
    },
    // 推送预制发票
    handlePushPreMakeInvoice(args) {
      const { selectedRecords, idList } = args
      const { valid, invoiceStatus, syncStatus } = this.verifyInvoiceStatus({
        data: selectedRecords,
        checkStatus: true,
        checkSyncStatus: true
      })
      if (!valid || invoiceStatus != InvoiceStatus.complete || syncStatus != SyncStatus.notSynced) {
        this.$toast({
          content: this.$t('请选择财务已确认且未同步的数据'),
          type: 'warning'
        })
      } else {
        // 状态: 财务已确认 && 推送预制发票状态: 未推送
        this.postPushPreMakeInvoice({ ids: idList })
      }
    },
    // 申请退回sap
    handleApplyReturnToSap(args) {
      const { selectedRecords } = args
      if (selectedRecords.length > 1) {
        this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
        return
      }

      if (selectedRecords[0].syncStatus != SyncStatus.synced) {
        this.$toast({
          content: this.$t('请选择同步状态的数据'),
          type: 'warning'
        })
        return
      }
      // 退回
      this.$refs.backToSapDialog.dialogInit({
        title: this.$t('确定退回'),
        selectData: selectedRecords[0]
      })
    },
    // CellTool
    handleClickCellTool(args) {
      const { data, tool } = args
      if (tool.id === 'SendBack' || tool.id === 'SendBack1') {
        // 退回
        this.$refs.sendBackDialog.dialogInit({
          title: this.$t('确定退回'),
          selectData: data
        })
      } else if (tool.id === 'ConfirmInvoice') {
        // 采购确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定采购确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方采购确认
            this.postCustomerReconciliationInvoiceV2purchaserConfirm(params)
          }
        })
      } else if (tool.id === 'ConfirmInvoiceFinance') {
        // 财务确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定财务确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方财务确认
            this.postCustomerReconciliationInvoiceV2FinanceConfirm(params)
          }
        })
      } else if (tool.id === 'BackToSap') {
        this.$refs.backToSapDialog.dialogInit({
          title: this.$t('确定退回'),
          selectData: data
        })
      }
    },
    // 对账单外部接口-对账单推送第三方共享财务
    postCustomerReconciliationInvoiceV2PushSharedFinance(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2PushSharedFinance(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发票推送预制发票
    postPushPreMakeInvoice(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postPushPreMadInvoice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方采购确认
    postCustomerReconciliationInvoiceV2purchaserConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2purchaserConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方财务确认
    postCustomerReconciliationInvoiceV2FinanceConfirm(params) {
      params.currentBu = localStorage.getItem('currentBu')
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FinanceConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 确定退回 弹框 调用api完成
    sendBackDialogConfirm() {
      // 刷新当前 Grid
      this.refreshColumns()
    },
    // 验证 发票、同步 状态是否统一
    verifyInvoiceStatus(args) {
      const { data, checkStatus, checkSyncStatus } = args
      let valid = true
      let invoiceStatus = null
      let syncStatus = null
      data.forEach((item, index) => {
        invoiceStatus = item.status // 发票状态
        syncStatus = item.syncStatus // 同步状态

        if (checkStatus && checkSyncStatus) {
          // 校验 发票状态、同步状态
          if (
            item &&
            data[index - 1] &&
            (item.status !== data[index - 1].status ||
              item.syncStatus !== data[index - 1].syncStatus)
          ) {
            valid = false
          }
        }
      })

      return { valid, invoiceStatus, syncStatus }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, fieldValue } = args
      if (field === 'reconciliationHeaderCodes') {
        // 点击 关联对账单号
        this.handleReconciliationHeaderCodes({
          data,
          fieldValue
        })
      } else if (field === 'attachementCount') {
        // 点击 发票附件 数量
        this.handleAttachementCount({ data })
      } else if (field === 'reconciliationItemCount') {
        // 点击 关联明细 数量
        this.handleReconciliationItemCountClick(data)
      }
    },
    // 点击 关联对账单号
    handleReconciliationHeaderCodes(args) {
      const { data, fieldValue: reconciliationCode } = args
      // 从 发票列表 跳转 单据详情页
      const reconciliationHeaderIdIndex = data.reconciliationHeaderCodes.findIndex(
        (item) => item === reconciliationCode
      )
      // 请求 api 根据单据 code、id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        condition: 'and',
        defaultRules: [
          {
            field: 'reconciliationCode',
            type: 'string',
            operator: 'equal',
            value: reconciliationCode
          },
          {
            field: 'id',
            type: 'string',
            operator: 'equal',
            value: data.reconciliationHeaderIds[reconciliationHeaderIdIndex].buyerHeaderId
          }
        ]
      }
      this.apiStartLoading()
      // 获取单据信息 租户级-发票协同-采方主单-对账信息
      this.$API.invoiceCollaboration
        .postReconciliationHeaderQueryBuilder(params)
        .then((res) => {
          this.apiEndLoading()
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          // 从 可开票单据 跳转 单据详情页
          this.goToInvoiceDetail({
            headerInfo: reconciliationHeaderInfo,
            entryType: ConstantType.look,
            status: data.status,
            id: data.id
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 发票附件 数量
    handleAttachementCount(args) {
      const { data } = args
      // 获取发票附件数据 后 显示 发票附件列表 弹框
      const params = {
        id: data.id
      }
      this.apiStartLoading()
      // 获取发票附件列表 查询附件列表
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FileList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileData = res?.data || []
          // 显示附件查看弹框
          const dialogParams = {
            fileData: fileData,
            isView: true,
            title: this.$t('发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 关联明细 数量
    handleReconciliationItemCountClick(data) {
      const params = {
        businessTypeCode: data.reconciliationBusinessTypeCode,
        reconciliationTypeCode: data.reconciliationTypeCode
      }
      this.apiStartLoading()
      // 获取 明细行 表头 采购对账字段配置
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          this.apiEndLoading()
          const fieldList = res?.data || []
          const fieldConfig = fieldList.find(
            (item) => item.code === TabCode.reconciliationField && item.checkStatus
          )
          const fields = fieldConfig?.fieldResponseList || []
          const columnData = formatTableColumnData({
            table: Table.statementDetails,
            data: fields
          })
          // 显示明细行列表弹框 查看关联 明细行、高低开
          this.$refs.billDetailsTableDialog.dialogInit({
            title: this.$t('关联明细行列表'),
            actionType: BillDetailsTableDialogActionType.view,
            dataItemAsyncConfig: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/customer-reconciliation-invoice-v2/item-page-with-invoice`, // 发票协同-供方-查询对账明细
              condition: 'and',
              params: {
                dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'invoiceId',
                  operator: 'equal',
                  value: data.id
                }
              ]
            },
            dataItemColumnData: columnData,
            // 高低开
            highLowInfoAsyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLow/queryByInvoiceId`,
              params: {
                id: data.id // 发票id
              },
              recordsPosition: 'data'
            }
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到 发票详情页面
    goToInvoiceDetail(data) {
      const { headerInfo, entryType, status, id } = data
      // 发票详情 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationCode, // 对账单号
        headerInfo, // 头部信息 行数据
        entryType // 页面类型
      }
      localStorage.setItem('summaryV2Data', JSON.stringify(params))
      const isGeneral = headerInfo?.businessTypeCode === 'BTTCL004' ? true : false // 是否通采
      this.$router.push({
        name: isGeneral ? 'invoice-detail-v2' : 'invoice-detail',
        query: { fromType: 'list', status, id }
      })
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
