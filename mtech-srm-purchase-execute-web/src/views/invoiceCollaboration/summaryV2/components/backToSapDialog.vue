<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div class="return-sap-dialog">
      <p class="tips">
        注意：退回SAP的对账单，视同废弃，对账数据会释放回待对账中，需重新发起对账，请谨慎操作！
      </p>
      <mt-form ref="ruleForm" :model="formData" :rules="rules">
        <mt-form-item :label="$t('对账单号')">
          <mt-input :disabled="true" v-model="reconciliationHeaderCodes"></mt-input>
        </mt-form-item>
        <mt-form-item prop="backTime" :label="$t('过账日期')">
          <mt-date-picker
            v-model="formData.backTime"
            :placeholder="$t('选择日期')"
            :show-clear-button="false"
            :disabled="isDisabled"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
export default {
  components: {},
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      dialogTitle: '',
      rules: {
        backTime: [
          {
            required: true,
            message: this.$t('请选择'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      selectData: {}, // 选中的数据
      reconciliationHeaderCodes: '',
      formData: {
        backTime: ''
      }
    }
  },
  mounted() {},

  methods: {
    // 初始化
    dialogInit(entryInfo) {
      const { title, selectData } = entryInfo
      this.selectData = cloneDeep(selectData)
      this.dialogTitle = title // 弹框名称
      this.reconciliationHeaderCodes = selectData.reconciliationHeaderCodes
      // this.formData.backTime = ''
      this.$refs.dialog.ejsRef.show()
    },

    onOpen(args) {
      args.preventFocus = true
    },

    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const params = {
            id: this.selectData.id, // 发票id
            backTime: dayjs(this.formData.backTime).format('YYYY-MM-DD HH:mm:ss')
          }
          this.apiStartLoading()
          // 发票状态为 6（采购退回sap）则调用财务退回接口 ： 采购申请退回
          this.$API.invoiceCollaboration[
            this.selectData.status === 6 ? 'financeBackSap' : 'purchaseBackSap'
          ](params)
            .then((res) => {
              this.apiEndLoading()
              if (res?.code == 200) {
                this.handleClose()
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm')
              }
            })
            .catch(() => {
              this.apiEndLoading()
            })
        }
      })
    },

    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
<style lang="scss">
.return-sap-dialog {
  padding-top: 20px;
  .tips {
    color: red;
    font-size: 16px;
    margin-bottom: 20px;
  }
}
</style>
