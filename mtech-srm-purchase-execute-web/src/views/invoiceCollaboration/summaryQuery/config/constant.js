import { i18n } from '@/main.js'

export const ConstantType = {
  Edit: '1', // 编辑
  Look: '2' // 查看
}

export const Table = {
  summary: 'summary', // 发票列表
  statementDetails: 'statementDetails' // 关联单据明细列表
}
// tab code
export const TabCode = {
  highLowInfo: 'highLowInfo', // 高低开信息
  operationLog: 'operationLog', // 操作日志
  reconciliationFile: 'reconciliationFile', // 相关附件
  reconciliationField: 'reconciliationField', // 对账明细
  invoiceList: 'invoiceList', // 发票清单
  purHeaderFile: 'purHeaderFile', // 采方-整单附件
  supHeaderFile: 'supHeaderFile' // 供方-整单附件
}

// 发票状态 0-待提交、1-待审核、2-待财务审核、3-完成、4-已退回、5-已删除
export const InvoiceStatus = {
  pendingSubmission: 0, // 待提交
  pendingReview: 1, // 待审核
  pendingFinancialReview: 2, // 待财务审核
  complete: 3, // 完成
  returned: 4, // 已退回
  deleted: 5, // 已删除
  purchaseBack: 6, // 采购回退SAP
  financeBack: 7 // 退回sap
}
// 发票状态
export const InvoiceStatusConst = {
  [InvoiceStatus.pendingSubmission]: i18n.t('待提交'),
  [InvoiceStatus.pendingReview]: i18n.t('待确认'), // 待审核
  [InvoiceStatus.pendingFinancialReview]: i18n.t('采购已确认'), // 待财务审核
  [InvoiceStatus.complete]: i18n.t('财务已确认'), // 完成
  [InvoiceStatus.returned]: i18n.t('已退回'),
  [InvoiceStatus.deleted]: i18n.t('已删除'),
  [InvoiceStatus.purchaseBack]: i18n.t('采购回退SAP'),
  [InvoiceStatus.financeBack]: i18n.t('退回SAP')
}
// 发票状态 对应的 css class
export const InvoiceStatusCssClass = {
  [InvoiceStatus.pendingSubmission]: 'col-active',
  [InvoiceStatus.pendingReview]: 'col-active',
  [InvoiceStatus.pendingFinancialReview]: 'col-normal',
  [InvoiceStatus.complete]: 'col-normal',
  [InvoiceStatus.returned]: 'col-published',
  [InvoiceStatus.deleted]: 'col-inactive',
  [InvoiceStatus.purchaseBack]: 'col-inactive',
  [InvoiceStatus.financeBack]: 'col-inactive'
}
// 发票状态 对应的 Options
export const InvoiceStatusOptions = [
  {
    // 待提交
    value: InvoiceStatus.pendingSubmission,
    text: InvoiceStatusConst[InvoiceStatus.pendingSubmission],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingSubmission]
  },
  {
    // 待审核
    value: InvoiceStatus.pendingReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingReview]
  },
  {
    // 待财务审核
    value: InvoiceStatus.pendingFinancialReview,
    text: InvoiceStatusConst[InvoiceStatus.pendingFinancialReview],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.pendingFinancialReview]
  },
  {
    // 完成
    value: InvoiceStatus.complete,
    text: InvoiceStatusConst[InvoiceStatus.complete],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.complete]
  },
  {
    // 已退回
    value: InvoiceStatus.returned,
    text: InvoiceStatusConst[InvoiceStatus.returned],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.returned]
  },
  {
    // 已删除
    value: InvoiceStatus.deleted,
    text: InvoiceStatusConst[InvoiceStatus.deleted],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.deleted]
  },
  {
    // 采购退回sap
    value: InvoiceStatus.purchaseBack,
    text: InvoiceStatusConst[InvoiceStatus.purchaseBack],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.purchaseBack]
  },
  {
    // 财务退回sap
    value: InvoiceStatus.financeBack,
    text: InvoiceStatusConst[InvoiceStatus.financeBack],
    cssClass: InvoiceStatusCssClass[InvoiceStatus.financeBack]
  }
]

// 同步状态 0:否 1:是 2:同步中
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0, // 否
  synchronizing: 2 // 同步中
}
// 同步状态 对应的 Options
export const SyncStatusOptions = [
  {
    value: SyncStatus.notSynced,
    text: i18n.t('未同步'),
    cssClass: 'col-notSynced'
  },
  {
    value: SyncStatus.synced,
    text: i18n.t('已同步'),
    cssClass: 'col-synced'
  },
  {
    value: SyncStatus.synchronizing,
    text: i18n.t('同步中'),
    cssClass: 'col-synced'
  }
]

// 过账状态 0:未过账 1：已过账
export const PostingStatus = {
  posting: 0, // 未过账
  posted: 1 // 已过帐
}
// 过账状态
export const postingStatusOptions = [
  {
    value: PostingStatus.posting,
    text: i18n.t('未过账'),
    cssClass: ''
  },
  {
    value: PostingStatus.posted,
    text: i18n.t('已过帐'),
    cssClass: ''
  }
]

export const Toolbar = [
  {
    id: 'excelExport',
    icon: 'icon_solid_pushorder',
    permission: ['O_02_1410'],
    title: i18n.t('导出')
  },
  {
    id: 'DetailExcelExport',
    icon: 'icon_solid_pushorder',
    permission: ['O_02_1411'],
    title: i18n.t('开票明细导出')
  },
  {
    id: 'reject',
    icon: 'icon_solid_pushorder',
    permission: ['O_02_1767'],
    title: i18n.t('手工驳回')
  }
]

// 表格列数据
export const ColumnData = [
  {
    code: 'status',
    name: i18n.t('发票状态') // 发票状态: 0-待提交、1-待审核、2-待财务审核、3-完成、4-已退回、5-已删除
  },
  {
    code: 'theHeaderTypeName', // theHeaderTypeName FIXME: 暂时前端固定 对账单
    name: i18n.t('关联单据类型')
  },
  {
    code: 'syncStatus',
    name: i18n.t('同步状态')
  },
  {
    code: 'postingStatus',
    name: i18n.t('过账状态')
  },
  {
    code: 'codeRel',
    name: i18n.t('外部关联单据号')
  },
  {
    code: 'invoiceNum',
    name: i18n.t('发票号')
  },
  {
    code: 'preMadeInvoiceCode',
    name: i18n.t('预制发票号')
  },
  {
    code: 'invoiceCode',
    name: i18n.t('发票代码')
  },
  {
    code: 'checkCode',
    name: i18n.t('后六位校验码')
  },
  {
    code: 'companyCode',
    name: i18n.t('公司')
    // companyName
  },
  {
    code: 'purchaserName',
    name: i18n.t('采购员')
  },
  {
    code: 'supplierCode',
    name: i18n.t('供应商')
    // supplierName
  },
  {
    code: 'reconciliationHeaderCodes',
    name: i18n.t('关联单据号')
  },
  {
    code: 'invoiceUntaxAmount',
    name: i18n.t('发票未税金额')
  },
  {
    code: 'invoiceTaxAmount',
    name: i18n.t('发票税额')
  },
  {
    code: 'invoiceTaxedAmount',
    name: i18n.t('发票含税金额')
  },
  {
    code: 'reconciliationUntaxAmount',
    name: i18n.t('汇总未税金额')
  },
  {
    code: 'reconciliationTaxAmount',
    name: i18n.t('汇总税额')
  },
  {
    code: 'reconciliationTaxedAmount',
    name: i18n.t('汇总含税金额')
  },
  {
    code: 'reconciliationItemCount', // 明细行数量
    name: i18n.t('明细行')
  },
  {
    code: 'invoiceTime',
    name: i18n.t('开票日期')
  },
  {
    code: 'currencyName',
    name: i18n.t('币种')
  },
  {
    code: 'invoiceTypeName',
    name: i18n.t('发票类型')
  },
  {
    code: 'customerRemark',
    name: i18n.t('采方备注')
  },
  {
    code: 'supplierRemark',
    name: i18n.t('供方备注')
  },
  {
    code: 'preMadeInvoiceMessage',
    name: i18n.t('SAP返回信息')
  },
  {
    code: 'attachementCount', // 附件数量
    name: i18n.t('发票附件')
  },
  {
    code: 'createUserName',
    name: i18n.t('创建人')
  },
  {
    code: 'createTime',
    name: i18n.t('创建时间')
  },
  {
    code: 'confirmTime',
    name: i18n.t('采购确认时间')
  },
  {
    code: 'reviewTime',
    name: i18n.t('财务审核时间')
  },
  {
    code: 'operation',
    name: i18n.t('操作')
  }
]

// 来源类型（枚举） 0: 上游流入1:第三方接口
export const SourceTypeOptions = [
  {
    value: 0,
    text: i18n.t('上游流入'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('第三方接口'),
    cssClass: ''
  }
]

// 单据状态 0:待对账 1:已创建对账单
export const ReconciliationDetailsStatusOptions = [
  {
    value: 0,
    text: i18n.t('待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('已创建对账单'),
    cssClass: ''
  }
]

// 提前开票 0:否 1:是
export const AdvanceInvoicingOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 冻结标记 0:否 1:是
export const FrozenStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否预付 0-否；1-是
export const PrePayStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]

// 是否暂估价 0-否；1-是
export const ProvisionalEstimateStatusOptions = [
  {
    value: 0,
    text: i18n.t('否'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('是'),
    cssClass: ''
  }
]
// 是否执行价 0-是；1-否
export const ProvisionalEstimateStatusOptions1 = [
  {
    value: 0,
    text: i18n.t('是'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('否'),
    cssClass: ''
  }
]
// 待对账类型:0-采购待对账；1-销售待对账
export const ReconciliationDetailsTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购待对账'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('销售待对账'),
    cssClass: ''
  }
]

// 正式价标识 0-无正式价；1-有正式价
export const RealPriceStatusOptions = [
  {
    value: 0,
    text: i18n.t('无正式价'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('有正式价'),
    cssClass: ''
  }
]

// 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
export const InOutTypeOptions = [
  {
    value: 0,
    text: i18n.t('采购入库'),
    cssClass: ''
  },
  {
    value: 1,
    text: i18n.t('采购出库'),
    cssClass: ''
  },
  {
    value: 2,
    text: i18n.t('销售出库'),
    cssClass: ''
  },
  {
    value: 3,
    text: i18n.t('销售退回'),
    cssClass: ''
  }
]
// 操作日志 表格列数据
export const LogColumnData = [
  {
    fieldCode: 'operTypeName',
    fieldName: i18n.t('操作类型')
  },
  {
    fieldCode: 'operDescription',
    fieldName: i18n.t('操作内容') // 操作描述 操作内容
  },
  {
    fieldCode: 'createUserName',
    fieldName: i18n.t('操作人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('操作时间')
  }
]
