import { i18n } from '@/main.js'
import inputView from '../components/inputView.vue'
import { MasterDataSelect } from '@/utils/constant'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'ConfigAdd',
    icon: 'icon_solid_Createorder',
    permission: ['O_02_0540'],
    title: i18n.t('新增')
  },
  {
    id: 'ConfigDelete',
    icon: 'icon_table_delete',
    permission: ['O_02_0541'],
    title: i18n.t('删除')
  },
  {
    id: 'ConfigActive',
    icon: 'icon_solid_Activateorder',
    permission: ['O_02_0542'],
    title: i18n.t('启用')
  },
  {
    id: 'ConfigInactive',
    icon: 'icon_solid_Pauseorder',
    permission: ['O_02_0543'],
    title: i18n.t('停用')
  },
  {
    id: 'ConfigImport',
    icon: 'icon_solid_Import',
    permission: ['O_02_0544'],
    title: i18n.t('导入')
  },
  {
    id: 'ConfigExport',
    icon: 'icon_solid_export',
    permission: ['O_02_0545'],
    title: i18n.t('导出')
  }
]

// 表格列数据
export const ColumnData = [
  {
    fieldCode: 'serialNumber', // 前端定义
    fieldName: i18n.t('序号')
  },
  {
    fieldCode: 'status', // 停用/启用 启用状态:0-未启用,1-启用
    fieldName: i18n.t('状态')
  },
  {
    fieldCode: 'siteCode', // 工厂 code-name
    fieldName: i18n.t('工厂')
    // siteName
    // siteId 工厂id
    // siteCode 工厂编码
  },
  {
    fieldCode: 'name', // 司机姓名
    fieldName: i18n.t('司机姓名')
  },
  {
    fieldCode: 'idCard', // 司机身份证号
    fieldName: i18n.t('司机身份证号'),
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'license', // 车牌号
    fieldName: i18n.t('车牌号')
  },
  {
    fieldCode: 'contact', // 司机手机号
    fieldName: i18n.t('司机手机号'),
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'createType', // 创建方式
    fieldName: i18n.t('创建方式')
  },
  {
    fieldCode: 'remark', // 备注
    fieldName: i18n.t('备注')
  },
  {
    fieldCode: 'createUserName', // 创建人
    fieldName: i18n.t('创建人')
  },
  {
    fieldCode: 'createTime',
    fieldName: i18n.t('创建时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    fieldCode: 'updateUserName', // 更新人
    fieldName: i18n.t('更新人')
  },
  {
    fieldCode: 'updateTime',
    fieldName: i18n.t('更新时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]

// 弹出框类型
export const DialogActionType = {
  Add: 0, // 新增
  Edit: 1 // 编辑
}

// 配置状态 1:启用 2:停用
export const ConfigStatus = {
  active: 1, // 启用
  inactive: 2 // 停用
}
// 配置状态 1:启用 2:停用
export const ConfigStatusConst = {
  [ConfigStatus.active]: i18n.t('启用'),
  [ConfigStatus.inactive]: i18n.t('停用')
}
// 配置状态 对应的 css class
export const ConfigStatusCssClass = {
  [ConfigStatus.active]: 'col-active', // 启用
  [ConfigStatus.inactive]: 'col-inactive' // 停用
}
// 配置状态 Options
export const ConfigStatusOptions = [
  {
    // 启用
    text: ConfigStatusConst[ConfigStatus.active],
    value: ConfigStatus.active,
    cssClass: ConfigStatusCssClass[ConfigStatus.active]
  },
  {
    // 停用
    text: ConfigStatusConst[ConfigStatus.inactive],
    value: ConfigStatus.inactive,
    cssClass: ConfigStatusCssClass[ConfigStatus.inactive]
  }
]

// 创建方式：1-手动,2-自动
export const CreateType = {
  manual: 1, // 手动
  automatic: 2 // 自动
}
// 创建方式：1-手动,2-自动
export const CreateTypeConst = {
  [CreateType.manual]: i18n.t('手动'),
  [CreateType.automatic]: i18n.t('自动')
}
// 创建方式 Options
export const CreateTypeOptions = [
  {
    // 手动
    value: CreateType.manual,
    text: CreateTypeConst[CreateType.manual],
    cssClass: ''
  },
  {
    // 自动
    value: CreateType.automatic,
    text: CreateTypeConst[CreateType.automatic],
    cssClass: ''
  }
]
