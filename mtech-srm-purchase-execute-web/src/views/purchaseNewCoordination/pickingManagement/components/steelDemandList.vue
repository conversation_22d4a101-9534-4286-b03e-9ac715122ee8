<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="demandCode" :label="$t('需求单编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.demandCode"
                :show-clear-button="true"
                :placeholder="$t('请输入需求单编号')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusSearchOptions"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :show-select-all="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="siteCode" :label="$t('工厂')" class="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.siteCode"
                :url="$API.masterData.getSiteListUrl"
                :placeholder="$t('请选择工厂')"
                :fields="{ text: 'siteName', value: 'siteCode' }"
                :search-fields="['siteName', 'siteCode']"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('钣金厂')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :placeholder="$t('请选择钣金厂')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              />
            </mt-form-item>
            <mt-form-item prop="sendAddress" :label="$t('送货地址')" label-style="top">
              <mt-input
                v-model="searchFormModel.sendAddress"
                :show-clear-button="true"
                :placeholder="$t('请输入送货地址')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="updateUserName" :label="$t('更新人')" label-style="top">
              <mt-input
                v-model="searchFormModel.updateUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入更新人')"
              />
            </mt-form-item>
            <mt-form-item prop="updateTime" :label="$t('更新时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.updateTime"
                @change="(e) => handleDateTimeChange(e, 'updateTime')"
                :placeholder="$t('请选择更新时间')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { listColumnData, statusSearchOptions } from '../config/steelDemandColumn.js'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      statusSearchOptions,
      searchFormModel: {
        status: []
      },
      pageConfig: [
        {
          gridId: '47504a8d-8415-4747-89ce-0d53a194ab71',
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          toolbar: {
            useBaseConfig: false,
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          useToolTemplate: false,
          grid: {
            columnData: listColumnData,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/steelDemand/queryBuyerPage'
            }
          }
        }
      ]
    }
  },
  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'demandCode') {
        this.$router.push({
          path: `steel-request-detail`,
          query: {
            timeStamp: new Date().getTime(),
            id: e.data.id
          }
        })
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
