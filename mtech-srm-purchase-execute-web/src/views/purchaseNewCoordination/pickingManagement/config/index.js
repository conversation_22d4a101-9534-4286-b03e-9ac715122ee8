import { i18n } from '@/main.js'
import $utils from '@/utils/utils'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI领料单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已出单'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' },
          { value: 10, text: i18n.t('已退回'), cssClass: 'col-active' }
        ]
      },
      cellTools: [
        {
          id: 'submit',
          icon: 'icon_solid_upload',
          title: i18n.t('提交'),
          visibleCondition: (data) => {
            let isShow = false
            if (data.status == 0) {
              isShow = true
            }
            return isShow
          }
        },
        // {
        //   id: "delete",
        //   icon: "icon_solid_Delete",
        //   title: i18n.t("删除"),
        //   visibleCondition: (data) => {
        //     let isShow = false;
        //     // 新建-采方-提交、删除
        //     if (data.status == 0 ) {
        //       isShow = true;
        //     }
        //     return isShow;
        //   },
        // },
        {
          id: 'cancel',
          icon: 'icon_solid_pushorder',
          title: i18n.t('取消'),
          visibleCondition: (data) => {
            let isShow = false
            // 待确认-采方-取消
            if (data.status == 1) {
              isShow = true
            }
            return isShow
          }
        }
      ]
    },
    {
      width: '95',
      field: 'siteCode',
      headerText: i18n.t('工厂编号')
    },
    {
      width: '225',
      field: 'siteName',
      headerText: i18n.t('工厂名称')
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('原材料供应商编码')
    },
    {
      width: '200',
      field: 'supplierName',
      headerText: i18n.t('原材料供应商名称')
    },
    {
      width: '105',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓编码')
    },
    {
      width: '115',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称')
    },
    {
      width: '230',
      field: 'processorCode',
      headerText: i18n.t('领料供应商编码')
    },
    {
      width: '200',
      field: 'processorName',
      headerText: i18n.t('领料供应商名称')
    },
    // {
    //   width: "150",
    //   field: "vmiWarehouseAddress",
    //   headerText: i18n.t("送货地址"),
    // },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    }
  ],
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI领料单号')
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已出单'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' },
          { value: 10, text: i18n.t('已退回'), cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '170',
      field: 'steelDemandCode',
      headerText: i18n.t('调料需求单号')
    },
    {
      width: '170',
      field: 'steelDemandLineNo',
      headerText: i18n.t('调料需求单行号')
    },
    {
      width: '95',
      field: 'siteCode',
      headerText: i18n.t('工厂编号')
    },
    {
      width: '225',
      field: 'siteName',
      headerText: i18n.t('工厂名称')
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('原材料供应商编码')
    },
    {
      width: '195',
      field: 'supplierName',
      headerText: i18n.t('原材料供应商名称')
    },
    {
      width: '115',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓编码')
    },

    {
      width: '120',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称')
    },
    {
      width: '115',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '395',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '110',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    {
      width: '110',
      field: 'countLimit',
      headerText: i18n.t('可领料数量')
    },
    {
      width: '95',
      field: 'checkCount',
      headerText: i18n.t('领料数量')
    },
    {
      width: '140',
      field: 'orderCode',
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '65',
      field: 'itemUnit',
      headerText: i18n.t('单位'),
      template: () => {
        return {
          template: Vue.component('actionInput', {
            template: `<div>{{data.itemUnit}}-{{data.itemUnitDescription}}</div>`,
            data: function () {
              return { data: {} }
            },
            mounted() {},
            methods: {}
          })
        }
      }
    },
    {
      width: '235',
      field: 'processorCode',
      headerText: i18n.t('领料供应商编码')
    },
    {
      width: '200',
      field: 'processorName',
      headerText: i18n.t('领料供应商名称')
    },
    // {
    //   width: "150",
    //   field: "vmiWarehouseAddress",
    //   headerText: i18n.t("送货地址"),
    // },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单日期'),
      // searchOptions:{
      //   elementType: "date-range",
      // },
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      type: 'date',
      valueConverter: {
        type: 'function',
        filter: (data) => {
          // 时间戳转日期
          return $utils.formateTime(new Date(+data), 'YYYY-mm-dd HH:MM:SS')
        }
      }
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    }
  ]
}
export const createColumnObj = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('PO')
  },
  {
    width: '150',
    field: 'lineNo',
    headerText: i18n.t('PO行')
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('卷重')
  },
  {
    width: '150',
    field: 'itemUnitDescription',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('接收人')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('接收时间'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  }
]
