<!-- 模具台账 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('模具编码')" prop="mouldCode">
          <mt-input
            v-model="searchFormModel.mouldCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具名称')" prop="mouldName">
          <mt-input
            v-model="searchFormModel.mouldName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具类型')" prop="mouldType">
          <mt-select
            v-model="searchFormModel.mouldType"
            :data-source="mouldTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('开模类型')" prop="mouldClassify">
          <mt-select
            v-model="searchFormModel.mouldClassify"
            :data-source="mouldClassifyOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('工序类型')" prop="operationType">
          <mt-select
            v-model="searchFormModel.operationType"
            :data-source="operationTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('使用状态-MES')" prop="useStatusMes">
          <mt-input
            v-model="searchFormModel.useStatusMes"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('管理状态-PLM')" prop="managementStateList">
          <mt-multi-select
            v-model="searchFormModel.managementStateList"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具开制厂家编码')" prop="companyCode">
          <mt-input
            v-model="searchFormModel.companyCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具开制厂家名称')" prop="companyName">
          <mt-input
            v-model="searchFormModel.companyName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具开制供应商编码')" prop="supplierCode">
          <mt-input
            v-model="searchFormModel.supplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具开制供应商名称')" prop="supplierName">
          <mt-input
            v-model="searchFormModel.supplierName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('当前使用供应商编码')" prop="currentSupplierCode">
          <mt-input
            v-model="searchFormModel.currentSupplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('当前使用供应商名称')" prop="currentSupplierName">
          <mt-input
            v-model="searchFormModel.currentSupplierName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('当前模具地址')" prop="currentMouldAddress">
          <mt-input
            v-model="searchFormModel.currentMouldAddress"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('上一次使用供应商编码')" prop="lastSupplierCode">
          <mt-input
            v-model="searchFormModel.lastSupplierCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('上一次使用供应商')" prop="lastSupplierName">
          <mt-input
            v-model="searchFormModel.lastSupplierName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('资产来源')" prop="sourceAssets">
          <mt-input
            v-model="searchFormModel.sourceAssets"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('项目来源')" prop="projectSource">
          <mt-input
            v-model="searchFormModel.projectSource"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('模具账套编码')" prop="mouldAccountCode">
          <mt-input
            v-model="searchFormModel.mouldAccountCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('合同编号')" prop="contractNumber">
          <mt-input
            v-model="searchFormModel.contractNumber"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('开发部门')" prop="developmentDepartment">
          <mt-input
            v-model="searchFormModel.developmentDepartment"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('项目编码')" prop="projectCode">
          <mt-input
            v-model="searchFormModel.projectCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('项目名称')" prop="projectName">
          <mt-input
            v-model="searchFormModel.projectName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('产品标识')" prop="productIdentification">
          <mt-input
            v-model="searchFormModel.productIdentification"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('法人帐套')" prop="corporateBook">
          <mt-input
            v-model="searchFormModel.corporateBook"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('系列名称')" prop="seriesName">
          <mt-input
            v-model="searchFormModel.seriesName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('成本中心')" prop="costCenter">
          <mt-input
            v-model="searchFormModel.costCenter"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('图号')" prop="mapNumber">
          <mt-input
            v-model="searchFormModel.mapNumber"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('图纸名称')" prop="drawingTitle">
          <mt-input
            v-model="searchFormModel.drawingTitle"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('开模申请单号')" prop="applicationForm">
          <mt-input
            v-model="searchFormModel.applicationForm"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="1c3023be-952d-4068-830b-d97f4e95db4a"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
      @changeColsVisible="handleChangeCols"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #moldHistoryDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleCheck(row)">
            {{ $t('履历表') }}
          </span>
        </div>
      </template>
      <template #estimatedPriceDefault="{ row }">
        <div>
          <span>{{ row.isShow ? row.estimatedPrice : '***' }} </span>
          <i
            v-if="hasPerm"
            :class="row.isShow ? 'vxe-icon-eye-fill' : 'vxe-icon-eye-fill-close'"
            style="margin-left: 5px; cursor: pointer"
            @click="showPrice(row)"
          />
        </div>
      </template>
      <template #operateDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleClick(row)">
            {{ $t('详情') }}
          </span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  columnData,
  mouldTypeOptions,
  mouldClassifyOptions,
  operationTypeOptions,
  statusOptions
} from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        {
          code: 'edit',
          name: this.$t('编辑'),
          status: 'info',
          loading: false,
          permission: ['O_02_1782']
        },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          loading: false,
          permission: ['O_02_1783']
        },
        {
          code: 'updateStatus',
          name: this.$t('修改管理状态'),
          status: 'info',
          loading: false,
          permission: ['O_02_1784']
        },
        {
          code: 'batchEdit',
          name: this.$t('批量编辑'),
          status: 'info',
          loading: false,
          permission: ['O_02_1790']
        },
        {
          code: 'sync',
          name: this.$t('同步MES'),
          status: 'info',
          loading: false,
          permission: ['O_02_1853']
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      mouldTypeOptions,
      mouldClassifyOptions,
      operationTypeOptions,
      statusOptions,

      includeColumnFiledNames: '',

      hasPerm: false // 是否有查看金额权限
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  async created() {
    await this.getPermission()
    this.getTableData()
    this.includeColumnFiledNames = this.getColumnField(columnData)
  },
  methods: {
    getPermission() {
      this.$API.moldManagement.verifyPermissionApi().then((res) => {
        if (res.code === 200) {
          this.hasPerm = res.data
        }
      })
    },
    showPrice(row) {
      row.isShow = !row.isShow
    },
    handleChangeCols(list) {
      this.includeColumnFiledNames = this.getColumnField(list)
    },
    getColumnField(arr) {
      let list = arr.map((item) => {
        if (!(['checkbox', 'seq'].includes(item?.type) || ['operate'].includes(item?.field))) {
          return item.field
        }
      })
      list.push('price')
      return list.filter((item) => item !== undefined).join(',')
    },
    dateChange(e) {
      if (e.startDate) {
        this.searchFormModel['startDate'] = this.getUnix(dayjs(e.startDate))
        this.searchFormModel['endDate'] = this.getUnix(dayjs(e.endDate))
      } else {
        this.searchFormModel['startDate'] = null
        this.searchFormModel['endDate'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .pageMouldMainApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records.map((item) => {
          return {
            isShow: false,
            ...item
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['edit', 'delete', 'updateStatus', 'batchEdit']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && e.code === 'edit') {
        this.$toast({ content: this.$t('只能选择一行进行编辑操作'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && e.code === 'updateStatus') {
        this.$toast({ content: this.$t('只能选择一行进行修改管理状态操作'), type: 'warning' })
        return
      }
      if (selectedRecords.length < 2 && e.code === 'batchEdit') {
        this.$toast({ content: this.$t('至少选择两行数据进行批量编辑操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'edit':
          this.handleEdit(selectedRecords[0])
          break
        case 'export':
          e.loading = true
          this.handleExport(e, selectedRecords)
          break
        case 'updateStatus':
          this.handleUpdateStatus(selectedRecords[0])
          break
        case 'batchEdit':
          this.handleBatchEdit(selectedRecords)
          break
        case 'sync':
          this.handleSync(selectedRecords)
          break
        default:
          break
      }
    },
    handleEdit(row) {
      this.$router.push({
        name: 'mould-detail',
        query: {
          type: 'edit',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleExport(e, selectedRecords) {
      const ids = selectedRecords.map((v) => v.id)
      const params = {
        ids,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.moldManagement
        .exportMouldMainApi(params, this.includeColumnFiledNames)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleUpdateStatus(row) {
      this.$dialog({
        data: {
          title: this.$t('修改管理状态'),
          row
        },
        modal: () => import('./components/UpdateStatusDialog.vue'),
        success: async (params) => {
          this.$API.moldManagement.updateStatusMouldMainApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },
    handleBatchEdit(selectedRecords) {
      let hasProcess = selectedRecords.some((v) => v.operationType === '2')
      let allProcess = selectedRecords.every((v) => v.operationType === '2')
      if (hasProcess && !allProcess) {
        this.$toast({
          content: this.$t('选中模具工序模非同一种类型，请去除后再发起'),
          type: 'warning'
        })
        return
      }
      const ids = selectedRecords.map((v) => v.id)
      this.$dialog({
        data: {
          title: this.$t('批量编辑'),
          ids
        },
        modal: () => import('./components/BatchUpdateDialog.vue'),
        success: async (params) => {
          this.$API.moldManagement.batchSaveMouldMainApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },
    handleSync(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认同步MES？')
        },
        success: () => {
          let ids = selectedRecords.map((v) => v.id)
          this.$API.moldManagement.syncMouldMainApi(ids).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.handleSearch()
            }
          })
        }
      })
    },
    // 查看履历表
    handleCheck(row) {
      this.$dialog({
        data: {
          title: this.$t('履历表'),
          id: row.id
        },
        modal: () => import('./components/ResumeDialog.vue'),
        success: () => {}
      })
    },
    handleClick(row) {
      this.$router.push({
        name: 'mould-detail',
        query: {
          type: 'check',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>
