import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const columnData = [
  {
    width: '200',
    field: 'reconciliationCode',
    headerText: i18n.t('对账单号'),
    cellTools: [
      // {
      //   id: "feedback",
      //   icon: "icon_list_feedback",
      //   title: i18n.t("反馈"),
      //   permission: ["O_02_0396"],
      //   visibleCondition: (data) => {
      //     return data.sourcePath == 1 && data.status == 1;
      //   },
      // },
    ],
    searchOptions: {
      maxQueryValueLength: 20000,
      operator: 'likeright'
    }
  },
  {
    width: '160',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      //0-未发布，1. 待反馈，2. 反馈正常，3. 反馈异常，-1. 已关闭
      map: [
        { value: 0, text: i18n.t('未发布'), cssClass: 'col-active' },
        { value: 1, text: i18n.t('待反馈'), cssClass: 'col-published' },
        { value: 2, text: i18n.t('反馈正常'), cssClass: 'col-normal' },
        { value: 3, text: i18n.t('反馈异常'), cssClass: 'col-abnormal' },
        { value: -1, text: i18n.t('已关闭'), cssClass: 'col-inactive' }
      ]
    },
    cellTools: [
      {
        id: 'close',
        icon: 'icon_list_close',
        title: i18n.t('关闭'),
        permission: ['O_02_0398'],
        visibleCondition: (data) => {
          return data.sourcePath == 0 && data.status == 0 && data.invoiceStatus == 0
        }
      }
      // {
      //   id: "publish",
      //   icon: "icon_list_issue",
      //   title: i18n.t("发布"),
      //   permission: ["O_02_0396"],
      //   visibleCondition: (data) => {
      //     return data.sourcePath == 0 && (data.status == 0 || data.status == 3);
      //   },
      // },
      // {
      //   id: "cancelPublish",
      //   icon: "icon_table_cancel",
      //   title: i18n.t("取消发布"),
      //   permission: ["O_02_0400"],
      //   visibleCondition: (data) => {
      //     return data.sourcePath == 0 && data.status == 1;
      //   },
      // },
    ]
  },
  {
    // width: "150",
    field: 'sourcePath',
    headerText: i18n.t('创建方'),
    valueConverter: {
      type: 'map',
      // 来源途径 0:采方 1:供方
      map: { 0: i18n.t('采方'), 1: i18n.t('供方') }
    }
  },
  {
    field: 'reconciliationTypeName',
    headerText: i18n.t('对账类型')
  },
  {
    width: '150',
    field: 'syncSapStatus',
    headerText: i18n.t('SAP同步状态'),
    searchOptions: {
      operator: 'likeright'
    },
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: '未同步',
        1: '同步成功',
        2: '同步成功',
        3: '同步失败'
      }
    }
  },
  {
    field: 'customProjectName',
    headerText: i18n.t('项目名称')
  },
  // {
  //   // width: "150",
  //   field: "companyCode",
  //   headerText: i18n.t("公司编号"),
  // },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  // {
  //   width: "230",
  //   field: "supplierCode",
  //   headerText: i18n.t("供应商编号"),
  // },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    searchOptions: MasterDataSelect.supplierAll,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    // width: "200",
    field: 'totalNumber',
    headerText: i18n.t('对账单总数量')
  },
  {
    // width: "200",
    field: 'executeUntaxedTotalPrice',
    headerText: i18n.t('执行未税总价')
  },
  {
    // width: "200",
    field: 'taxAmount',
    headerText: i18n.t('税额')
  },
  {
    // width: "200",
    field: 'executeTaxedTotalPrice',
    headerText: i18n.t('执行含税总价')
  },
  {
    width: '250',
    field: 'currencyCode',
    headerText: i18n.t('币种'),
    searchOptions: MasterDataSelect.moneyType,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.currencyCode, data?.currencyName)
    }
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    // width: "150",
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: MasterDataSelect.dateRange
  },
  {
    // width: "200",
    field: 'publishTime',
    headerText: i18n.t('发布时间'),
    searchOptions: MasterDataSelect.dateRange
  },
  {
    // width: "200",
    field: 'remark',
    headerText: i18n.t('采方备注')
  },
  {
    // width: "200",
    field: 'feedbackRemark',
    headerText: i18n.t('供方备注')
  },
  {
    field: 'operation',
    headerText: i18n.t('操作'),
    valueConverter: {
      type: 'function',
      filter: () => {
        return `查看日志`
      }
    },
    cellTools: []
  }
]
// 操作日志 表格列数据
export const LogColumnData = [
  {
    field: 'operTypeName',
    headerText: i18n.t('操作类型'),
    width: '150'
  },
  {
    field: 'operDescription',
    headerText: i18n.t('操作内容'), // 操作描述 操作内容
    width: '150'
  },
  {
    field: 'createUserName',
    headerText: i18n.t('操作人'),
    width: '150'
  },
  {
    field: 'createTime',
    headerText: i18n.t('操作时间'),
    width: '150'
  }
]
