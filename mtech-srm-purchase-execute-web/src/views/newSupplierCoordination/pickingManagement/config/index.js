import { i18n } from '@/main.js'
import $utils from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI领料单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已出单'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' },
          { value: 10, text: i18n.t('已退回'), cssClass: 'col-active' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已确认'), value: 2 },
          { label: i18n.t('已出单'), value: 8 },
          { label: i18n.t('已取消'), value: 9 },
          { label: i18n.t('已退回'), value: 10 }
        ],
        fields: { text: 'label', value: 'value' }
      },
      cellTools: [
        // {
        //   id: "confirmss",
        //   icon: "a-icon_MultipleChoice_on",
        //   title: i18n.t("确认"),
        //   visibleCondition: (data) => {
        //     let isShow = false;
        //     if (data.status == 1) {
        //       isShow = true;
        //     }
        //     return isShow;
        //   },
        // },
        // {
        //   id: "delete",
        //   icon: "icon_solid_Delete",
        //   title: i18n.t("提交"),
        //   visibleCondition: (data) => {
        //     let isShow = false;
        //     // 新建-采方-提交、删除
        //     if (data.status == 0) {
        //       isShow = true;
        //     }
        //     return isShow;
        //   },
        // },
        {
          id: 'cancel',
          icon: 'icon_solid_pushorder',
          title: i18n.t('退回'),
          visibleCondition: (data) => {
            let isShow = false
            // 待确认-采方-取消
            if (data.status == 1) {
              isShow = true
            }
            return isShow
          }
        }
      ]
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('钢材供应商'),
      searchOptions: MasterDataSelect.supplierVMI,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('钢材供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSteel,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'processorCode',
      headerText: i18n.t('钣金供应商'),
      searchOptions: MasterDataSelect.SteelSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.processorCode, data?.processorName)
      }
    },
    {
      width: '0',
      field: 'processorName',
      headerText: i18n.t('钣金供应商名称'),
      ignore: true
    },
    {
      width: '150',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
      // type: "date",
      // valueConverter: {
      //   type: "function",
      //   filter: (data) => {
      //     return $utils.formateTime(new Date(+data));
      //   },
      // },
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    }
  ],
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI领料单号'),
      cellTools: []
    },
    {
      width: '150',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已出单'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' },
          { value: 10, text: i18n.t('已退回'), cssClass: 'col-active' }
        ]
      },
      searchOptions: {
        elementType: 'multi-select',
        operator: 'in',
        multiple: true,
        dataSource: [
          { label: i18n.t('新建'), value: 0 },
          { label: i18n.t('待确认'), value: 1 },
          { label: i18n.t('已确认'), value: 2 },
          { label: i18n.t('已出单'), value: 8 },
          { label: i18n.t('已取消'), value: 9 },
          { label: i18n.t('已退回'), value: 10 }
        ],
        fields: { text: 'label', value: 'value' }
      }
    },
    {
      width: '260',
      field: 'siteCode',
      headerText: i18n.t('工厂'),
      searchOptions: MasterDataSelect.factorySupplierAddress,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.siteCode, data?.siteName)
      }
    },
    {
      width: '0',
      field: 'siteName',
      headerText: i18n.t('工厂名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'supplierCode',
      headerText: i18n.t('钢材供应商'),
      searchOptions: MasterDataSelect.supplierVMI,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
      }
    },
    {
      width: '0',
      field: 'supplierName',
      headerText: i18n.t('钢材供应商名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓'),
      searchOptions: MasterDataSelect.vmiWarehouseSteel,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.vmiWarehouseCode, data?.vmiWarehouseName)
      }
    },
    {
      width: '0',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      ignore: true
    },
    {
      width: '260',
      field: 'itemCode', //supplierCode
      headerText: i18n.t('物料'),
      searchOptions: MasterDataSelect.itemSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.itemCode, data?.itemName)
      }
    },
    {
      width: '0',
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      ignore: true
    },
    {
      width: '110',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    {
      width: '110',
      field: 'countLimit',
      headerText: i18n.t('可领料数量')
    },
    {
      width: '95',
      field: 'checkCount',
      headerText: i18n.t('领料数量')
    },
    {
      width: '140',
      field: 'orderCode',
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '65',
      field: 'itemUnit',
      headerText: i18n.t('单位')
    },
    {
      width: '260',
      field: 'processorCode',
      headerText: i18n.t('钣金供应商'),
      searchOptions: MasterDataSelect.SteelSupplier,
      valueAccessor: (field, data) => {
        return judgeFormatCodeName(data?.processorCode, data?.processorName)
      }
    },
    {
      width: '0',
      field: 'processorName',
      headerText: i18n.t('钣金供应商名称'),
      ignore: true
    },
    {
      width: '150',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '150',
      field: 'createTime',
      headerText: i18n.t('制单日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      // type: "date",
      valueConverter: {
        type: 'function',
        filter: (data) => {
          return $utils.formateTime(new Date(+data), 'YYYY-mm-dd HH:MM:SS')
        }
      }
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    }
  ]
}
