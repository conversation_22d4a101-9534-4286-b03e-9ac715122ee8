<template>
  <!-- 送货单收货-采方 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :current-tab="currentTabIndex"
      :permission-obj="permissionObj"
      @cellEdit="cellEdit"
      @rowSelecting="rowSelecting"
      @handleClickCellTool="handleClickCellTool"
      @handleClickToolBar="handleClickToolBar"
      :template-config="pageConfig1"
    >
    </mt-template-page>
    <!-- <mt-template-page :template-config="pageConfig1"> </mt-template-page> -->
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData, columnData2, Status, SyncStatus, columnData3 } from './config/index.js'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  data() {
    let currentTabIndex = 0
    if (this.$route.query.from == 'mydelivery0') {
      currentTabIndex = 1
    }
    return {
      userInfo: null,
      editdata: [], //编辑维护的数组

      currentTabIndex,
      apiWaitingQuantity: 0, // 调用的api正在等待数
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'MainSheetView', permissionCode: 'T_02_0026' },
          { dataPermission: 'DetailView', permissionCode: 'T_02_0027' }
        ]
      },
      pageConfig1: [
        {
          title: this.$t('主单视图'),
          dataPermission: 'MainSheetView',
          permissionCode: 'T_02_0026',
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置（打开筛选、刷新、设置）
          toolbar: [
            {
              id: 'close',
              icon: 'icon_table_cancel',
              permission: ['O_02_0496'],
              title: this.$t('关闭')
            },
            {
              id: 'export1',
              icon: 'icon_solid_Import',
              title: this.$t('导出')
            },
            {
              id: 'syncSap',
              icon: 'icon_table_synchronize',
              permission: ['O_02_1206'],
              title: this.$t('同步')
            }
          ],
          gridId: this.$tableUUID.receiptAndDelivery.receiptListProcessing.masterOrder,
          grid: {
            columnData: columnData,
            lineIndex: 1,
            autoWidthColumns: columnData.length + 1,
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: false,
              url: `${BASE_TENANT}/buyerOrderDelivery/pro/query`,
              defaultRules:
                this.$route.query.from == 'mytodo1'
                  ? JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules?.length
                    ? JSON.parse(sessionStorage.getItem('todoDetail')).defaultRules
                    : []
                  : [
                      {
                        field: 'shipType', // 送货方式 0-直送,1-非直送
                        operator: 'equal',
                        value: 0
                      }
                    ],
              rules: [
                {
                  field: 'createTime',
                  label: '创建时间',
                  operator: 'between',
                  type: 'string',
                  value: [
                    new Date(dayjs().subtract(180, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
                    new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
                  ]
                }
              ]
            },
            frozenColumns: 1,
            defaultSearchItem: [
              {
                field: 'createTime',
                headerText: this.$t('创建时间')
              }
            ]
          }
        },
        {
          title: this.$t('待收货'),
          dataPermission: 'DetailView',
          permissionCode: 'T_02_0027',
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useCombinationSelection: false,
          useBaseConfig: true, // 使用组件中的toolbar配置（打开筛选、刷新、设置）
          toolbar: [
            {
              id: 'Submit',
              icon: 'icon_solid_Import',
              title: this.$t('提交')
            }
          ],

          gridId: this.$tableUUID.receiptAndDelivery.receiptListProcessing.detailView,
          grid: {
            columnData: columnData2,
            lineIndex: 1,
            autoWidthColumns: columnData2.length + 1,
            dataSource: [],
            // allowPaging: false,
            asyncConfig: {
              ignoreDefaultSearch: false,

              // page: {
              //   current: 1,
              //   size: 10000,
              // },
              url: `${BASE_TENANT}/buyerOrderDelivery/pro/query/item/page`,
              defaultRules:
                this.$route.query.from == 'mydelivery0'
                  ? [
                      {
                        condition: null,
                        field: 'status',
                        label: null,
                        type: 'string',
                        operator: 'equal',
                        value: 2,
                        rules: null
                      },
                      {
                        condition: null,
                        field: 'shipType',
                        label: null,
                        type: 'string',
                        operator: 'equal',
                        value: 0,
                        rules: null
                      }
                    ]
                  : [
                      {
                        field: 'shipType', // 送货方式 0-直送,1-非直送
                        operator: 'equal',
                        value: 0
                      },
                      {
                        field: 'status', //
                        operator: 'equal',
                        value: 2
                      }
                    ],
              rules: [
                {
                  field: 'createTime',
                  label: '创建时间',
                  operator: 'between',
                  type: 'string',
                  value: [
                    new Date(dayjs().subtract(180, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
                    new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
                  ]
                }
              ],
              serializeList: (list) => {
                console.log(list, this.editdata)
                if (this.editdata.length > 0) {
                  this.editdata.forEach((item) => {
                    let index = list.findIndex((e) => e.id == item.id)
                    if (index > -1) {
                      list[index] = { ...item }
                    }
                  })
                  return list
                } else {
                  return list
                }
              }
            },
            frozenColumns: 1,
            defaultSearchItem: [
              {
                field: 'createTime',
                headerText: this.$t('创建时间')
              }
            ]
          }
        },
        {
          title: this.$t('明细视图'),
          // dataPermission: "DetailView",
          // permissionCode: "T_02_0027",
          useToolTemplate: false, // 使用预置表格操作按钮(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置（打开筛选、刷新、设置）
          toolbar: [
            {
              id: 'export',
              icon: 'icon_solid_Import',
              title: this.$t('导出')
            }
          ],

          gridId: 'a6e1d320-7754-440f-848a-79a56984a7cb',
          grid: {
            columnData: columnData3,
            lineIndex: 1,
            autoWidthColumns: columnData3.length + 1,
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: true,

              url: `${BASE_TENANT}/buyerOrderDelivery/pro/query/item/page`,
              defaultRules: [
                {
                  field: 'shipType', // 送货方式 0-直送,1-非直送
                  operator: 'equal',
                  value: 0
                }
              ],
              rules: [
                {
                  field: 'createTime',
                  label: '创建时间',
                  operator: 'between',
                  type: 'string',
                  value: [
                    new Date(dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')).getTime(),
                    new Date(dayjs().format('YYYY-MM-DD 23:59:59')).getTime()
                  ]
                }
              ]
            },
            frozenColumns: 1,
            defaultSearchItem: [
              {
                field: 'createTime',
                headerText: this.$t('创建时间')
              }
            ]
          }
        }
      ]
    }
  },
  mounted() {},

  methods: {
    //编辑行内
    //编辑行内
    cellEdit(e) {
      let { type, status, data } = e
      let flag = false
      if (status) {
        if (this.editdata && this.editdata.length < 1) {
          this.editdata.push(data)
        } else {
          for (let i = 0; i < this.editdata.length; i++) {
            if (this.editdata[i].id !== data.id) {
              flag = true
              break
            }
          }
          if (flag) {
            let arr = this.editdata.map((item) => item.id)
            let indexOf = arr.indexOf(data.id)
            if (indexOf == -1) {
              this.editdata.push(data)
            }
          }
          this.editdata.map((item) => {
            if (type === 'text' && item.id === data.id) {
              item.rejectReason = data.rejectReason
            } else if (type === 'number' && item.id === data.id) {
              item.rejectQuantity = data.rejectQuantity
              item.receiveQuantity = data.receiveQuantity
            }
          })
        }
      } else {
        if (this.editdata && this.editdata.length < 1) return
        this.editdata.map((item, i) => {
          if (item.id === data.id) {
            this.editdata.splice(i, 1)
          }
        })
      }

      console.log(this.editdata)
    },
    handleClickToolBar(args) {
      const { toolbar, gridRef } = args
      const selectToolBar = ['close', 'syncSap']
      const selectedIds = []
      const selectedData = gridRef.getMtechGridRecords()
      selectedData.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (selectedIds.length === 0 && selectToolBar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (toolbar.id === 'Submit') {
        this.handleClickToolBarSubmit(selectedData)
      }
      if (args.toolbar.id == 'export') {
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[2].gridId))?.visibleCols
        const headerMap = {}
        if (obj !== undefined && obj.length) {
          obj?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        } else {
          this.pageConfig1[2].grid.columnData?.forEach((i) => {
            if (i.field !== 'customChecked' && i.field !== 'id') {
              headerMap[i.field] = i.headerText
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        console.log(queryBuilderRules)
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery
          .buyerOrderDeliveryQueryExport(params, headerMap)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            download({ fileName: `${fileName}`, blob: res.data })
          })
        return
      }
      if (args.toolbar.id == 'export1') {
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(sessionStorage.getItem(this.pageConfig1[0].gridId))?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnData.forEach((item) => {
            if (item.fieldCode) {
              field.push(item.fieldCode)
            }
          })
        }

        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules,
          defaultRules: [
            {
              field: 'shipType', // 送货方式 0-直送,1-非直送
              operator: 'equal',
              value: 0
            }
          ],
          sortedColumnStr: field.toString()
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.receiptAndDelivery.buyerOrderQueryExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        return
      }
      if (toolbar.id === 'close') {
        this.deleteList(selectedIds)
      } else if (toolbar.id === 'syncSap') {
        const { valid } = this.checkSelectedValid({
          selectedData,
          actionType: toolbar.id
        })
        if (valid) {
          this.postSupplierSyncSap({ selectedIds })
        } else {
          this.$toast({
            content: this.$t('请先选择状态为已完成，同步状态非同步成功的数据！'),
            type: 'warning'
          })
        }
      }
    },
    //复选框事件
    rowSelecting(e) {
      if (e?.rowIndexes?.length < 1) return
      if (e.data instanceof Array) return

      let Obj = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getCurrentViewRecords()
      )
      Obj.map((item, i) => {
        item.index = i
      })
      //获取当前页勾选的值
      let currentSelect = cloneDeep(
        this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords()
      )
      currentSelect.push(e.data)
      currentSelect = [...new Set(currentSelect)]
      if (Obj instanceof Array && Obj.length > 0) {
        let mapArr = []
        for (let i = 0; i < currentSelect.length; i++) {
          for (let j = 0; j < Obj.length; j++) {
            if (currentSelect[i]?.deliveryCode === Obj[j]?.deliveryCode) {
              mapArr.push(Obj[j])
            }
          }
        }
        mapArr = mapArr.map((item) => item.index)
        if (!(e.data instanceof Array) && e?.rowIndexes?.length > 0 && mapArr.length > 0) {
          this.$refs.templateRef.getCurrentUsefulRef().ejsRef.selectRows(mapArr)
        }
      }
    },
    //点击提交
    handleClickToolBarSubmit(selectedData) {
      let _selectedData = cloneDeep(selectedData)
      if (_selectedData.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //没有编辑但是勾选的值
      let newarr = this.editdata.map((item) => item.id)
      let incluArr = _selectedData.filter((item) => !newarr.includes(item.id))
      incluArr.map((item) => {
        item.receiveQuantity = item.deliveryQuantity
      })
      //编辑的值替换勾选的值
      this.editdata.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      //勾选但未编辑的值 ---替换
      incluArr.forEach((item) => {
        let index = _selectedData.findIndex((e) => e.id == item.id)
        if (index > -1) {
          _selectedData[index] = item
        }
      })
      console.log(_selectedData)
      this.doSubmit(_selectedData)
    },
    doSubmit(_selectedData) {
      let row = _selectedData
      let rowLine = []
      row.forEach((item) => {
        rowLine.push({
          deliveryCode: item.deliveryCode
        })
      })
      var run = {}
      rowLine = rowLine.reduce(function (item, next) {
        run[next.deliveryCode] ? '' : (run[next.deliveryCode] = true && item.push(next))
        return item
      }, [])
      let obj = []
      rowLine.forEach((item) => {
        let newArr = row.filter((e) => item.deliveryCode === e.deliveryCode)
        let newArrObj = newArr.map((r) => {
          return {
            id: r.id,
            receiveQuantity: r.receiveQuantity,
            rejectReason: r.rejectReason
          }
        })
        obj.push({
          proTenantId: newArr[0].proTenantId,
          request: newArrObj
        })
      })

      this.$store.commit('startLoading')
      this.$API.receiptAndDelivery
        .postNewBuyerOrderDeliveryConfirm(obj)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code == 200) {
            this.editdata = []
            this.$refs.templateRef.refreshCurrentGridData()
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
    },
    handleClickCellTool(e) {
      if (e.tool.title === this.$t('确认收货')) {
        e.data.outsourcedType = Number(e.data.outsourcedType)

        localStorage.setItem('deliverDetailSupplier', JSON.stringify(e.data))
        this.$router.push({
          name: 'receipt-list-detail',
          query: {
            id: e.data.id,
            timeStamp: new Date().getTime()
          }
        })
      }
      if (e.tool.title === this.$t('取消')) {
        let _id = []
        _id.push(e.data.id)
        this.deleteList(_id)
      }
      // 附件
      if (e.tool.id === 'delivery_scanning') {
        this.handleFile(e.data, e.tool.id)
      }
    },
    handleFile(row, type) {
      const typeMap = {
        delivery_scanning: 9
      }
      this.$dialog({
        modal: () => import('./../deliverListTV/components/fileManage.vue'),
        data: {
          title: this.$t('附件上传'),
          id: row.id,
          type: typeMap[type],
          row,
          pageType: 'receiptListProcess'
        }
      })
    },
    // 同步sap
    postSupplierSyncSap(args) {
      const { selectedIds } = args
      const params = selectedIds
      this.apiStartLoading()
      this.$API.receiptAndDelivery
        .postSupplierSyncSap(params)
        .then(() => {
          this.apiEndLoading()
          // if (res.code == 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
          // }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 判断选择的数据是否符合要求
    checkSelectedValid(data) {
      const { selectedData, actionType } = data
      let valid = true
      let validStatus = true // 状态
      let validSyncStatus = true // 同步状态
      if (selectedData && selectedData.length > 0) {
        if (actionType === 'syncSap') {
          // 同步sap
          for (let i = 0; i < selectedData.length; i++) {
            // 可同步条件： 状态 已完成，同步状态 同步失败

            if (selectedData[i].status !== Status.completed) {
              // 状态 不为 已完成
              validStatus = false
              valid = false
            }

            if (selectedData[i].syncReceiveStatus === SyncStatus.synced) {
              // 同步状态 为 同步成功
              validSyncStatus = false
              valid = false
            }
          }
        }
      }

      return {
        valid,
        validStatus,
        validSyncStatus
      }
    },
    // 取消
    deleteList(id) {
      this.$API.receiptAndDelivery.postBuyerOrderDeliveryClose(id).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style></style>
