import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 状态 1-新建,2-发货中,3-已完成,4-已取消,5-已关闭
export const Status = {
  inDelivery: 2,
  completed: 3,
  cancelled: 4,
  closed: 5
}

// 同步状态 0-未同步，1-同步中，2-同步成功，3-同步失败
export const SyncStatus = {
  notSynced: 0,
  synching: 1,
  synced: 2,
  failed: 3
}

const timeDate = (dataKey, hasTime) => {
  // const { dataKey, hasTime } = args;
  // console.log(args);
  const template = () => {
    return {
      template: Vue.component('date', {
        template: `<div><div>{{data[dataKey] | dateFormat}}</div><div v-if="hasTime">{{data[dataKey] | timeFormat}}</div></div>`,
        data: function () {
          return { data: {}, dataKey, hasTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

const currentBu = localStorage.getItem('currentBu')

export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    searchOptions: {
      operator: 'likeright'
    },
    cssClass: '',
    cellTools: [
      {
        id: 'inactive',
        icon: 'icon_Editor',
        title: i18n.t('确认收货'),
        permission: ['O_02_0644', 'O_02_1678', 'O_02_1679'],
        visibleCondition: (data) =>
          currentBu === 'TV'
            ? data['status'] === 2 || (data['status'] === 3 && data['syncReceiveStatus'] !== 2)
            : data['status'] === 2
      }
    ]
  },
  {
    width: '66',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-active' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-active' }
      ]
    }
  },
  {
    width: '130',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    template: timeDate('sendTime', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '130',
    field: 'forecastArriveTime',
    headerText: i18n.t('需求日期'),
    template: timeDate('forecastArriveTime', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '110',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '220',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '110',
    field: 'proTenantCode',
    headerText: i18n.t('加工商编码')
  },
  {
    width: '220',
    field: 'proTenantName',
    headerText: i18n.t('加工商名称')
  },
  {
    width: '150',
    field: 'canFile',
    headerText: i18n.t('送货单扫描件'),
    cellTools: [
      {
        id: 'delivery_scanning',
        title: i18n.t('附件')
      }
    ]
  },
  {
    width: '100',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '220',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '100',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '155',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '120',
    field: 'warehouseCode',
    headerText: i18n.t('库存地点编码')
  },
  {
    width: '130',
    field: 'warehouseName',
    headerText: i18n.t('库存地点名称')
  },
  {
    width: '110',
    field: 'deliveryOrderType',
    headerText: i18n.t('送货单类型'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('采购订单'), cssClass: '' },
        { value: 2, text: i18n.t('交货计划'), cssClass: '' },
        { value: 3, text: i18n.t('JIT'), cssClass: '' },
        { value: 4, text: i18n.t('无需求'), cssClass: '' },
        { value: 5, text: i18n.t('vmi'), cssClass: '' },
        { value: 6, text: i18n.t('钢材'), cssClass: '' },
        { value: 7, text: i18n.t('屏发货指导'), cssClass: '' }
      ]
    }
  },
  {
    width: '100',
    field: 'outsourcedType',
    headerText: i18n.t('委外方式'),
    valueConverter: {
      type: 'map',
      map: [
        { value: '0', text: i18n.t('标准委外'), cssClass: '' },
        { value: '1', text: i18n.t('销售委外'), cssClass: '' },
        { value: '2', text: i18n.t('非委外'), cssClass: '' },
        { value: '3', text: i18n.t('工序委外'), cssClass: '' }
      ]
    }
  },
  {
    width: '130',
    field: 'sendAddressName',
    headerText: i18n.t('发货地点')
  },
  {
    width: '120',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.vmiWarehouseCode}}-{{data.vmiWarehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '98',
    field: 'syncReceiveStatus',
    headerText: i18n.t('同步状态'), // 0-未同步，1-同步中，2-同步成功，3-同步失败
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('未同步'), cssClass: '' },
        { value: 1, text: i18n.t('同步中'), cssClass: '' },
        { value: 2, text: i18n.t('同步成功'), cssClass: '' },
        { value: 3, text: i18n.t('同步失败'), cssClass: '' }
      ]
    }
  },
  {
    width: '195',
    field: 'syncReceiveDesc',
    headerText: i18n.t('同步信息')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('备注')
  }
]
export const columnData2 = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'deliveryCode',
    headerText: i18n.t('送货单号'),
    searchOptions: {
      operator: 'likeright'
    }
  },
  {
    width: '65',
    field: 'status',
    headerText: i18n.t('状态'),
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { value: 2, text: i18n.t('发货中'), cssClass: 'col-inactive' },
        { value: 3, text: i18n.t('已完成'), cssClass: 'col-inactive' },
        { value: 4, text: i18n.t('已取消'), cssClass: 'col-inactive' },
        { value: 5, text: i18n.t('已关闭'), cssClass: 'col-inactive' }
      ]
    }
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '220',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'orderCode',
    headerText: i18n.t('采购订单号')
  },
  {
    width: '130',
    field: 'lineNo',
    headerText: i18n.t('采购订单行号')
  },
  {
    width: '110',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '220',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '100',
    field: 'deliveryQuantity',
    headerText: i18n.t('发货数量')
  },
  {
    width: '150',
    field: 'receiveQuantity',
    headerText: i18n.t('收货数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<mt-input-number
          v-if="data.status ==2"
          v-model="receiveQuantity"
          :disabled="data.ifSteelVmi == 1"
          :min="0"
          :max="data.deliveryQuantity"
          precision="3"
          cssClass="e-outline"
          :show-clear-button="false"
          @input="handleInput"
          ></mt-input-number>
          <div v-else>{{receiveQuantity}}</div>
          `,
          data: function () {
            return {
              receiveQuantity: 0
            }
          },
          mounted() {
            if (this.data.receiveQuantity) {
              this.receiveQuantity = this.data.receiveQuantity
            } else {
              this.receiveQuantity = this.data.deliveryQuantity
            }
          },
          methods: {
            handleInput(e) {
              this.data.receiveQuantity = e
              let rejectQuantity =
                Number(this.data.deliveryQuantity) - Number(this.data.receiveQuantity)
              if (this.data.receiveQuantity > this.data.deliveryQuantity) {
                rejectQuantity = 0
              }
              let obj = {
                rejectQuantity,
                index: this.data.index,
                receiveQuantity: this.data.receiveQuantity
              }
              this.$bus.$emit(`rejectQuantity${obj.index}`, obj)
              this.$bus.$emit(`rejectReason${obj.index}`, obj)
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'receiveTime',
    headerText: i18n.t('收货日期'),
    template: timeDate('receiveTime', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '100',
    field: 'rejectQuantity',
    headerText: i18n.t('拒绝数量'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
          <div>{{dataObj.rejectQuantity}}</div>
          `,
          data: function () {
            return {
              dataObj: {
                rejectQuantity: 0
              }
            }
          },
          mounted() {
            if (this.data.rejectQuantity) {
              this.dataObj.rejectQuantity = this.data.rejectQuantity
            }
            this.$bus.$off(`rejectQuantity${this.data.index}`)
            this.$bus.$on(`rejectQuantity${this.data.index}`, (val) => {
              if (this.data.index === val.index) {
                this.$set(this.dataObj, 'rejectQuantity', val.rejectQuantity)
                this.$set(this.data, 'rejectQuantity', val.rejectQuantity)
                this.$set(this.data, 'receiveQuantity', val.receiveQuantity)
                if (this.data.rejectQuantity) {
                  console.log('1')
                  this.$parent.$emit(`cellEdit`, {
                    data: this.data,
                    type: 'number',
                    status: true
                  })
                } else {
                  console.log('2')
                  this.$parent.$emit(`cellEdit`, {
                    data: this.data,
                    type: 'number',
                    status: false
                  })
                }
              }
            })
          }
        })
      }
    }
  },
  {
    width: '200',
    field: 'rejectReason',
    headerText: i18n.t('拒绝原因'),
    headerTemplate: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('拒绝原因')}}</span>
              </div>
            `
        })
      }
    },
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `
        <div>
        <div v-if="rejectReasonFlag">{{dataObj.rejectReason}}</div>
          <mt-input
          id="rejectReason"
          v-else
          v-model="dataObj.rejectReason"
          @change="change"
        ></mt-input>
        </div>
          `,
          data: function () {
            return {
              dataObj: {
                rejectReason: ''
              },
              rejectReasonFlag: true
            }
          },
          mounted() {
            if (this.data.rejectReason) {
              if (this.data.status == 2) {
                this.rejectReasonFlag = false
              }
              this.dataObj.rejectReason = this.data.rejectReason
            }
            this.$bus.$off(`rejectReason${this.data.index}`)
            this.$bus.$on(`rejectReason${this.data.index}`, (val) => {
              if (this.data.index === val.index && val.rejectQuantity != 0) {
                if (this.data.rejectQuantity == val.rejectQuantity) {
                  this.dataObj.rejectReason = this.data.rejectReason
                }
                this.rejectReasonFlag = false
              } else if (this.data.index === val.index && val.rejectQuantity == 0) {
                this.rejectReasonFlag = true
                this.dataObj.rejectReason = ''
              }
            })
          },
          methods: {
            change() {
              if (!this.dataObj.rejectReason) return
              console.log('3')
              this.data.rejectReason = this.dataObj.rejectReason
              this.$parent.$emit(`cellEdit`, {
                data: this.data,
                type: 'text',
                status: true
              })
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '220',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '110',
    field: 'proTenantCode',
    headerText: i18n.t('加工商编码')
  },
  {
    width: '220',
    field: 'proTenantName',
    headerText: i18n.t('加工商名称')
  },
  {
    width: '130',
    field: 'sendTime',
    headerText: i18n.t('发货日期'),
    template: timeDate('sendTime', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '130',
    field: 'forecastArriveTime',
    headerText: i18n.t('需求日期'),
    template: timeDate('forecastArriveTime', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '100',
    field: 'demandTime',
    headerText: i18n.t('需求时间')
  },
  {
    width: '91',
    field: 'receiveAddressName',
    headerText: i18n.t('送货地址')
  },
  {
    width: '110',
    field: 'companyCode',
    headerText: i18n.t('公司编码')
  },
  {
    width: '220',
    field: 'companyName',
    headerText: i18n.t('公司名称')
  },
  {
    width: '91',
    field: 'workCenterName',
    headerText: i18n.t('工作中心')
  },
  {
    width: '150',
    field: 'processName',
    headerText: i18n.t('工序名称')
  },
  {
    width: '66',
    field: 'unitName',
    headerText: i18n.t('单位')
  },
  {
    width: '100',
    field: 'deliveryType',
    headerText: i18n.t('类型'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('采购订单'), cssClass: '' },
        { value: 2, text: i18n.t('交货计划'), cssClass: '' },
        { value: 3, text: i18n.t('JIT'), cssClass: '' },
        { value: 4, text: i18n.t('无需求'), cssClass: '' },
        { value: 5, text: i18n.t('vmi'), cssClass: '' },
        { value: 6, text: i18n.t('钢材'), cssClass: '' }
      ]
    }
  },
  {
    field: 'postingDate',
    headerText: i18n.t('过账日期'),
    template: timeDate('postingDate', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '150',
    field: 'deliveryNumber',
    headerText: i18n.t('交货编号')
  },
  {
    width: '85',
    field: 'jit',
    headerText: i18n.t('是否JIT'),
    valueConverter: {
      type: 'map',
      map: [
        { value: 0, text: i18n.t('否'), cssClass: '' },
        { value: 1, text: i18n.t('是'), cssClass: '' }
      ]
    }
  },
  {
    width: '150',
    field: 'jitDeliveryNumber',
    headerText: i18n.t('JIT编号'),
    searchOptions: {
      renameField: 'deliveryNumber'
    }
  },
  {
    width: '130',
    field: 'thirdTenantCode',
    headerText: i18n.t('第三方物流商'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.thirdTenantCode}}-{{data.thirdTenantName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓'),
    template: () => {
      return {
        template: Vue.component('actionInput', {
          template: `<div>{{data.vmiWarehouseCode}}-{{data.vmiWarehouseName}}</div>`,
          data: function () {
            return { data: {} }
          },
          mounted() {},
          methods: {}
        })
      }
    }
  },
  {
    width: '130',
    field: 'sendAddressName',
    headerText: i18n.t('发货地点')
  },
  {
    width: '85',
    field: 'bomCode',
    headerText: i18n.t('BOM号')
  },
  {
    width: '100',
    field: 'buyerOrgName',
    headerText: i18n.t('采购组')
  },
  {
    width: '130',
    field: 'deliveryRemark',
    headerText: i18n.t('送货单备注')
  },
  {
    width: '107',
    field: 'workOrderNo',
    headerText: i18n.t('关联工单号')
  },
  {
    width: '150',
    field: 'saleOrderNo',
    headerText: i18n.t('关联销售订单号')
  },
  {
    width: '150',
    field: 'saleOrderLineNo',
    headerText: i18n.t('关联销售订单行号')
  },
  {
    width: '150',
    field: 'productCode',
    headerText: i18n.t('关联产品代码')
  },
  {
    width: '91',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '91',
    field: 'limitQuantity',
    headerText: i18n.t('限量数量')
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('仓管员')
  },
  {
    width: '86',
    field: 'warehouseClerkName',
    headerText: i18n.t('调度员')
  },
  {
    width: '100',
    field: 'takeNo',
    headerText: i18n.t('车牌号')
  },
  {
    width: '150',
    field: 'cancelPersonName',
    headerText: i18n.t('取消人')
  },
  {
    width: '150',
    field: 'cancelTime',
    headerText: i18n.t('取消时间'),
    template: timeDate('cancelTime', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '150',
    field: 'collectorMark',
    headerText: i18n.t('代收标识')
  },
  {
    width: '150',
    field: 'collectorName',
    headerText: i18n.t('代收人')
  },
  {
    field: 'inputDate',
    headerText: i18n.t('凭证创建日期'),
    template: timeDate('inputDate', true),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
