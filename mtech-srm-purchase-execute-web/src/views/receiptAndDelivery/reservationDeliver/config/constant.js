import { i18n } from '@/main.js'
import Vue from 'vue'
import inputView from '../components/inputView.vue'
import { MasterDataSelect } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'

// Toolbar 按钮
export const Toolbar = [
  {
    id: 'BookPass',
    icon: 'icon_table_pass',
    permission: ['O_02_0498'],
    title: i18n.t('通过')
  },
  {
    id: 'BookReturn',
    icon: 'icon_table_return1',
    permission: ['O_02_0499'],
    title: i18n.t('退回')
  },
  {
    id: 'synchronization',
    permission: ['O_02_1625'],
    title: i18n.t('同步')
  },
  {
    id: 'export',
    title: i18n.t('导出')
  },
  {
    id: 'copy',
    title: i18n.t('复制送货单号')
  },
  {
    id: 'cancel',
    permission: ['O_02_1852'],
    title: i18n.t('取消')
  }
]

// 表格组件类型
export const ComponentType = {
  view: 'view',
  edit: 'edit',
  mustEdit: 'mustEdit'
}

// 状态 1-未预约,2-审批中,3-预约成功,4-预约拒绝
export const Status = {
  notYet: 1, // 未预约
  inProgress: 2, // 审批中
  success: 3, // 预约成功
  reject: 4, // 预约拒绝
  cancel: 5, // 已取消
  reservate: 8 // 预约中
}
// 状态 text
export const StatusText = {
  [Status.notYet]: i18n.t('未预约'),
  [Status.inProgress]: i18n.t('审批中'),
  [Status.success]: i18n.t('预约成功'),
  [Status.reject]: i18n.t('预约拒绝'),
  [Status.cancel]: i18n.t('已取消'),
  [Status.reservate]: i18n.t('预约中')
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 未预约
    value: Status.notYet,
    text: StatusText[Status.notYet],
    cssClass: 'col-active'
  },
  {
    // 审批中
    value: Status.inProgress,
    text: StatusText[Status.inProgress],
    cssClass: 'col-active'
  },
  {
    // 预约成功
    value: Status.success,
    text: StatusText[Status.success],
    cssClass: 'col-active'
  },
  {
    // 预约拒绝
    value: Status.reject,
    text: StatusText[Status.reject],
    cssClass: 'col-active'
  },
  {
    // 预约中
    value: Status.reservate,
    text: StatusText[Status.reservate],
    cssClass: 'col-active'
  },
  {
    // 已取消
    value: Status.cancel,
    text: StatusText[Status.cancel],
    cssClass: 'col-inactive'
  }
]

// 入园状态 入园装填:1-已入园,2-未入园
export const IntoStatus = {
  already: 1, // 已入园
  notYet: 2 // 未入园
}
// 入园状态 text
export const IntoStatusText = {
  [IntoStatus.already]: i18n.t('已入园'),
  [IntoStatus.notYet]: i18n.t('未入园')
}
// 入园状态 对应的 Options
export const IntoStatusOptions = [
  {
    // 已入园
    value: IntoStatus.already,
    text: IntoStatusText[IntoStatus.already],
    cssClass: ''
  },
  {
    // 未入园
    value: IntoStatus.notYet,
    text: IntoStatusText[IntoStatus.notYet],
    cssClass: ''
  }
]

// 交货方式 交货方式:1-采购订单,2-交货计划,3-JIT,4-无需求,5-vmi
export const DeliveryStatus = {
  purchase: 1, // 采购订单
  plan: 2, // 交货计划
  jit: 3, // JIT
  noNeed: 4, // 无需求
  vmi: 5, // VMI
  recovery: 9, // 包装物回收
  returnGoods: 10 // 物料退货
}
// 交货方式 text
export const DeliveryStatusText = {
  [DeliveryStatus.purchase]: i18n.t('采购订单'),
  [DeliveryStatus.plan]: i18n.t('交货计划'),
  [DeliveryStatus.jit]: i18n.t('JIT'),
  [DeliveryStatus.noNeed]: i18n.t('无需求'),
  [DeliveryStatus.vmi]: i18n.t('VMI'),
  [DeliveryStatus.recovery]: i18n.t('包装物回收'),
  [DeliveryStatus.returnGoods]: i18n.t('物料退货')
}

// 交货方式 对应的 Options
export const DeliveryStatusOptions = [
  {
    // 采购订单
    value: DeliveryStatus.purchase,
    text: DeliveryStatusText[DeliveryStatus.purchase],
    cssClass: ''
  },
  {
    // 交货计划
    value: DeliveryStatus.plan,
    text: DeliveryStatusText[DeliveryStatus.plan],
    cssClass: ''
  },
  {
    // JIT
    value: DeliveryStatus.jit,
    text: DeliveryStatusText[DeliveryStatus.jit],
    cssClass: ''
  },
  {
    // 无需求
    value: DeliveryStatus.noNeed,
    text: DeliveryStatusText[DeliveryStatus.noNeed],
    cssClass: ''
  },
  {
    // VMI
    value: DeliveryStatus.vmi,
    text: DeliveryStatusText[DeliveryStatus.vmi],
    cssClass: ''
  },
  {
    // 包装物回收
    value: DeliveryStatus.recovery,
    text: DeliveryStatusText[DeliveryStatus.recovery],
    cssClass: ''
  },
  {
    // 物料退货
    value: DeliveryStatus.returnGoods,
    text: DeliveryStatusText[DeliveryStatus.returnGoods],
    cssClass: ''
  }
]

// 同步状态 状态:0-未同步，1-同步失败，2同步成功
export const SyncStatus = {
  notSynced: 0, // 否
  fail: 1, // 失败
  synced: 2 // 是
}
// 交货方式 text
export const SyncStatusText = {
  [SyncStatus.notSynced]: i18n.t('未同步'),
  [SyncStatus.fail]: i18n.t('同步失败'),
  [SyncStatus.synced]: i18n.t('已同步')
}
// 交货方式 Class
export const SyncStatusClass = {
  [SyncStatus.notSynced]: 'col-notSynced',
  [SyncStatus.fail]: 'col-notSynced',
  [SyncStatus.synced]: 'col-synced'
}
// 交货方式 对应的 Options
export const SyncStatusOptions = [
  {
    value: SyncStatus.notSynced,
    text: SyncStatusText[SyncStatus.notSynced],
    cssClass: SyncStatusClass[SyncStatus.notSynced]
  },
  {
    value: SyncStatus.fail,
    text: SyncStatusText[SyncStatus.fail],
    cssClass: SyncStatusClass[SyncStatus.fail]
  },
  {
    value: SyncStatus.synced,
    text: SyncStatusText[SyncStatus.synced],
    cssClass: SyncStatusClass[SyncStatus.synced]
  }
]

// 单元格操作按钮
export const CellTools = [
  {
    id: 'BookPass',
    icon: '',
    title: i18n.t('通过'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.inProgress // 审批中
  },
  {
    id: 'BookReturn',
    icon: '',
    title: i18n.t('退回'),
    // permission: [""],
    visibleCondition: (data) => data.status == Status.inProgress // 审批中
  }
]

// 表格列数据
export const ColumnData = [
  {
    field: 'id',
    headerText: 'id',
    width: 0,
    visible: false,
    allowEditing: false,
    isPrimaryKey: true
  },
  {
    fieldCode: 'forecastCode', // 预约号 预约送货单号
    fieldName: i18n.t('预约号')
  },
  {
    fieldCode: 'deliveryCode', // 关联ASN
    fieldName: i18n.t('关联ASN')
  },
  {
    fieldCode: 'companyCode', // 公司 companyName code+name
    fieldName: i18n.t('公司')
  },
  {
    fieldCode: 'supplierCode', // 供应商 supplierName code+name
    fieldName: i18n.t('供应商')
  },
  {
    fieldCode: 'buyerOrgCode', // 采购组 buyerOrgName code+name
    fieldName: i18n.t('采购组')
  },
  {
    fieldCode: 'status', // 预约状态
    fieldName: i18n.t('预约状态')
  },
  {
    width: '120',
    fieldCode: 'onWayStatus',
    fieldName: i18n.t('在途状态'),
    searchOptions: {
      elementType: 'multi-select',
      showSelectAll: true,
      operator: 'in'
    },
    cssClass: '',
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
        { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
        { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
        { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
        { text: i18n.t('保安门岗确认'), value: 11, cssClass: 'col-active' },
        { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
        { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' },
        { text: i18n.t('已离园'), value: 12, cssClass: 'col-active' },
        { text: i18n.t('已到厂待卸货'), value: 7, cssClass: 'col-active' },
        { text: i18n.t('已卸货待报检'), value: 8, cssClass: 'col-active' }
      ]
    }
  },
  {
    fieldCode: 'syncStatus', // 同步状态 状态:0-未同步，1-同步失败，2同步成功
    fieldName: i18n.t('同步状态')
  },
  {
    fieldCode: 'intoStatus', // 入园状态
    fieldName: i18n.t('入园状态')
  },
  {
    fieldCode: 'vehicleLogistics', // 车辆物流
    fieldName: i18n.t('车辆物流'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('template-detail', {
          template: `
              <span v-if="data.carNo" style="color: #6386c1; cursor: pointer;" @click="toLogistics(data)">{{ $t('查看物流') }}</span>`,
          methods: {
            toLogistics(e) {
              const params = {
                ztpno: e.deliveryCode.toString(),
                busCode: e.forecastCode,
                busNum: e.carNo
              }
              this.$API.receiptAndDelivery.purQueryVehicleLogistics(params).then((res) => {
                if (res.code === 200) {
                  window.open(res.data.mapURL)
                }
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'qbjFile',
    fieldName: i18n.t('青白江监管账册资料'),
    allowEditing: false,
    template: () => {
      return {
        template: Vue.component('qbj-file', {
          template: `
              <span v-if="data.qbjFlag === 'Y'" style="color: #6386c1; cursor: pointer;" @click="handleDownload(data)">{{ $t('下载') }}</span>`,
          methods: {
            handleDownload(e) {
              const params = {
                id: e.id
              }
              this.$API.receiptAndDelivery.downloadBuyerForecastDeliveryApi(params).then((res) => {
                const fileName = getHeadersFileName(res)
                download({ fileName: `${fileName}`, blob: res.data })
              })
            }
          }
        })
      }
    }
  },
  {
    fieldCode: 'deliveryStatus', // 交货方式
    fieldName: i18n.t('交货方式')
  },
  {
    fieldCode: 'driverName', // 司机姓名
    fieldName: i18n.t('司机姓名')
  },
  {
    fieldCode: 'carNo', // 车牌号
    fieldName: i18n.t('车牌号')
  },
  {
    fieldCode: 'driverIdNo', // 司机身份证号
    fieldName: i18n.t('司机身份证号'),
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'driverPhone', // 司机手机号
    fieldName: i18n.t('司机手机号'),
    template: () => ({ template: inputView })
  },
  {
    fieldCode: 'quantity', // 件数
    fieldName: i18n.t('件数')
  },
  {
    fieldCode: 'subSiteCode',
    fieldName: i18n.t('分厂')
  },
  {
    fieldCode: 'gateNumber',
    fieldName: i18n.t('门岗号')
  },
  {
    fieldCode: 'forecastTime', // 预约时间
    fieldName: i18n.t('预约时间'),
    searchOptions: { ...MasterDataSelect.timeRange }
  },
  {
    fieldCode: 'dataSource',
    fieldName: i18n.t('预约来源'),
    valueConverter: {
      type: 'map',
      map: [
        { text: i18n.t('SRM'), value: 0, cssClass: 'col-active' },
        { text: i18n.t('TMS'), value: 1, cssClass: 'col-active' },
        { text: i18n.t('其他'), value: 2, cssClass: 'col-active' }
      ]
    }
  },
  {
    fieldCode: 'outerApptNo',
    fieldName: i18n.t('TMS预约单号'),
    width: 200
  },
  {
    fieldCode: 'warehouseCode',
    width: 250,
    fieldName: i18n.t('库存地点'),
    allowEditing: false,
    template: () => {
      const template = {
        template: `<div>{{data.warehouseCode}}-{{data.warehouse}}</div>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    fieldCode: 'sendAddressCode',
    width: 250,
    fieldName: i18n.t('送货地址'),
    allowEditing: false,
    template: () => {
      const template = {
        template: `<div>{{data.sendAddressCode}}-{{data.sendAddress}}</div>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    fieldCode: 'remark', // 备注
    fieldName: i18n.t('备注')
  },
  {
    fieldCode: 'accompanyNum', // 随车人数 accompanyList 前端定义 key
    fieldName: i18n.t('随车人数')
  },
  {
    fieldCode: 'accompanyInfo', // 随车信息 accompanyList 前端定义 key
    fieldName: i18n.t('随车信息')
  },
  {
    fieldCode: 'cancelPersonCode',
    fieldName: i18n.t('取消人'),
    allowEditing: false,
    template: () => {
      const template = {
        template: `<div>{{data.cancelPersonCode}}-{{data.cancelPersonName}}</div>`,
        data: function () {
          return { data: {} }
        },
        mounted() {},
        methods: {}
      }
      return { template }
    }
  },
  {
    fieldCode: 'checkInTimeStr',
    fieldName: i18n.t('签到时间'),
    allowEditing: false,
    ignore: true
  },
  {
    fieldCode: 'enterParkTimeStr',
    fieldName: i18n.t('入园时间'),
    allowEditing: false,
    ignore: true
  },
  {
    fieldCode: 'departTimeStr',
    fieldName: i18n.t('出车时间'),
    allowEditing: false,
    ignore: true
  }
]
