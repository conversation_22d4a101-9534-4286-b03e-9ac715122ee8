import { API } from '@mtech-common/http'

import { BASE_TENANT, PROXY_MDM_COMMON_TENANT } from '@/utils/constant'
import { PROXY_PRICE_TENANT } from '@/utils/constant'

export const NAME = 'receiptAndDelivery'

// 供方收发货供货计划-JIT创建送货单
export const postSupplierDeliverySupplyPlanJitCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/jit/create`, data)

// 供方收发货供货计划-JIT创建无订单送货单
export const postSupplierDeliverySupplyPlanJitCreateNoOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/jit/create/noOrder`, data)

// 供方收发货供货计划-JIT预创建送货单
export const postSupplierDeliverySupplyPlanJitPreCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/jit/preCreate`, data)

// 供方收发货供货计划-JIT供货计划列表
export const postSupplierDeliverySupplyPlanJitQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/jit/query`, data)

// 供方收发货供货计划-订单创建送货单
export const postSupplierDeliverySupplyPlanOrderCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/order/create`, data)

// 供方收发货供货计划-订单 预-创建送货单
export const postSupplierDeliverySupplyPlanOrderPreCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/order/preCreate`, data)

// 供方收发货供货计划-订单供货计划列表Excel导出
export const postSupplierDeliverySupplyPlanExportQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/export/query`, data, {
    responseType: 'blob'
  })

// 供方收发货供货计划-订单供货计划列表
export const postSupplierDeliverySupplyPlanOrderQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/order/query`, data)

// 供方收发货供货计划-交货计划创建送货单
export const postSupplierDeliverySupplyPlanPlanCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/plan/create`, data)

// 供方收发货供货计划-交货计划供货计划列表Excel导出
export const postSupplierDeliverySupplyPlanExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/plan/export?zeroFilter=1`, data, {
    responseType: 'blob'
  })

// 供方收发货供货计划-jit供货计划列表Excel导出
export const postSupplierDeliverySupplyJitExport = (data = {}, query) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/jit/export?zeroFilter=${query.query}`, data, {
    responseType: 'blob'
  })

// 采方收发货供货计划-订单供货计划列表Excel导出
export const postBuyerDeliveryPlanOrderExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliverySupplyPlan/order/export`, data, {
    responseType: 'blob'
  })

// 采方收发货供货计划-交货计划供货计划列表Excel导出
export const postBuyerDeliveryPlanPlanExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliverySupplyPlan/plan/export`, data, {
    responseType: 'blob'
  })

// 采方收发货供货计划-jit供货计划列表Excel导出
export const postBuyerDeliveryPlanJitExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliverySupplyPlan/jit/export`, data, {
    responseType: 'blob'
  })

// 供方收发货供货计划-交货计划创建无订单送货单
export const postSupplierDeliverySupplyPlanPlanCreateNoOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/plan/create/noOrder`, data)

// 供方收发货供货计划-交货计划预创建送货单
export const postSupplierDeliverySupplyPlanPlanPreCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/plan/preCreate`, data)

// 供方收发货供货计划-交货计划供货计划列表
export const postSupplierDeliverySupplyPlanPlanQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/plan/query`, data)
// ++++++++++++++++++条码打印 begin ++++++++++++++++++++
// 条码打印条码设置删除
export const barcodePrintConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintConfig/delete`, data)
// 条码打印条码设置保存
export const barcodePrintConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintConfig/save`, data)
// 条码设置TV保存
export const barcodeSetupTVSave = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/config/save`, data)
// 根据条码层级、工厂编码、物料编码获取最新历史保存记录
export const getConfigBarcodeTVApi = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/config/get/config`, data)
// 条码打印需求设置导出
export const barcodePrintConfigExport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintConfig/export`, data, {
    responseType: 'blob'
  })
// 条码打印设置 - 导出 - 泛智屏
export const barcodePrintConfigExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintConfig/export-tv`, data, {
    responseType: 'blob'
  })

// 条码打印需求管理 - 泛智屏 - 查询
export const barcodePrintRequestSearch = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintRequest/query-tv`, data)
}
// 条码打印需求管理保存
export const barcodePrintRequestsave = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/save`, data)
// 供方-条码需求管理-泛智屏-保存
export const barcodePrintRequestSaveTV = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/request/save`, data)
// 供方-条码需求管理-泛智屏 - 导入 - 泛智屏
export const barcodeRequestImportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/request/import`, data, {
    responseType: 'blob'
  })
// 供方-条码需求管理-泛智屏 - 导出 - 泛智屏
export const barcodeRequestExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/request/exportTemplate`, data, {
    responseType: 'blob'
  })
// 供方-条码需求管理-泛智屏-获取原厂物料标识
export const getMaterialIdentificationListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/request/queryMaterialFactory`, data)
// 条码打印需求管理删除
export const barcodePrintRequestDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/delete`, data)
// 条码打印需求管理导出
export const barcodePrintExternalExport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintExternal/export`, data, {
    responseType: 'blob'
  })
// 条码关联导出
export const barcodePrintRequestExport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/export`, data, {
    responseType: 'blob'
  })
// 条码打印-打印
export const barcodePrintRequestPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/print`, data, {
    responseType: 'blob'
  })
// 条码打印-打印 英文
export const barcodeEnPrintRequestPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/print?nationalFlag=1`, data, {
    responseType: 'blob'
  })
//条码需求查询
export const barcodePrintRequestQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/query`, data)
//条码关联内部保存
export const barcodePrintRecordSave = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRecord/associate`, data)
// 供方查询送货单明细
export const supplierOrderDeliveryItemPage = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDeliveryItem/page`, data)
// 供方-条码需求管理-泛智屏-根据工厂获取主单号
export const getDeliveryCodeBySite = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/item/supplier/page`, data)
// 物料替换单查询
export const getVmiAllocationOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiAllocationOrder/supplier-page-query`, data)
// 入库单查询
export const getReceiveOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/supplier-page-query`, data)
// 获取物料替换单详情
export const getVmiAllocationOrderDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-order/detail`, data)
// 获取入库单换单详情
export const getReceiveOrderDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/detail`, data)
// 供方送货单查询供应商
export const supplierOrderDeliverySupplier = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierOrderDelivery/supplier`, data)
// 根据工厂+供应商+物料+条码层级查找配置
export const barcodePrintConfigQueryOne = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintConfig/queryOne`, data)
}
//条码关联外部保存
export const barcodePrintExternalSave = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintExternal/save`, data)
}
//获取自动生成条码需求配置列表
export const barcodeAutoCreateConfigQuery = (data = {}) => {
  return API.get(`${BASE_TENANT}/barcodeAutoCreateConfig/query`, data)
}
//删除条码需求配置
export const barcodeAutoCreateConfigDelete = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodeAutoCreateConfig/delete`, data)
}
//保存生成条码需求配置
export const barcodeAutoCreateConfigSave = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodeAutoCreateConfig/save`, data)
}
// 条码内部同步
export const barcodePrintRecordSync = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintRecord/sync`, data)
}
// 条码外部同步
export const barcodePrintExternalSync = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintExternal/sync`, data)
}
// 条码关联外部删除
export const barcodePrintExternalDelete = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintExternal/delete`, data)
}
// 条码关联条码id列表
export const barcodePrintRecordtListCode = (data) => {
  return API.post(`${BASE_TENANT}/barcodePrintRecord/listCode`, data)
}
// 条码关联内部删除
export const barcodePrintRecordDelete = (data = {}) => {
  return API.post(`${BASE_TENANT}/barcodePrintRecord/delete`, data)
}
// ++++++++++++++++++条码打印 end ++++++++++++++++++++
// 供方送货单明细-主单id查询明细
export const postSupplierOrderDeliveryItemQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDeliveryItem/query`, data)

// 供方送货单主单-供方取消
export const supplierOrderDeliveryCancel = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/cancel`, data)

// 供方送货单主单-供方 - TV - 取消
export const supplierOrderDeliveryCancelTV = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/cancel`, data)

// 供方送货单列表-导出
export const supExportDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/supExportDelivery`, data, {
    responseType: 'blob'
  })

// 供方送货单明细-导出
export const supplierOrderDeliveryItemExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDeliveryItem/page/export`, data, {
    responseType: 'blob'
  })

// 新模式送货单 - 供方送货单明细-导出
export const newModeSupplierOrderDeliveryItemExport = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/item/supplier/export`, data, {
    responseType: 'blob'
  })

// 采方送货单列表-导出
export const buyerOrderDeliveryPurExportDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/purExportDelivery`, data, {
    responseType: 'blob'
  })

// 采方送货单明细-导出
export const buyerOrderDeliveryQueryExport = (data = {}, params) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query/item/page/export`, data, {
    responseType: 'blob',
    params
  })

// 采方送货单明细-导出   - 新
export const buyerOrderDeliveryQueryNewExport = (data = {}, params) =>
  API.post(`/statistics/tenant/supplier/delivery/item/view/v1/export`, data, {
    responseType: 'blob',
    params
  })

// 采方送货单明细-导出
export const buyerOrderDeliveryQueryExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/item/buyer/export`, data, {
    responseType: 'blob'
  })

// 采方送货单明细-导出    new
export const buyerOrderDeliveryQueryExportNewTv = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/item/buyer/export`, data, {
    responseType: 'blob'
  })

// 采方-导出栈板屏ID外围系统日志
export const buyerscreenIdlogExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/screen/exportLog`, data, {
    responseType: 'blob'
  })

// 供方送货单明细-导出    new
export const supplierOrderDeliveryQueryExportNewTv = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/item/supplier/export`, data, {
    responseType: 'blob'
  })

// 采方送货单头视图-导出
export const buyerOrderQueryExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query/export`, data, {
    responseType: 'blob'
  })

// 供方送货单头视图-导出
export const supplierOrderQueryExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/pro/query/export`, data, {
    responseType: 'blob'
  })
// 采方送货单明细-导出
// export const supplierOrderDeliveryItemProExport = (data = {}) =>
//   API.post(`${BASE_TENANT}/supplierOrderDeliveryItem/pro/page/export`, data, {
//     responseType: "blob",
//   });

// 供方送货单主单-供方打印
export const supplierOrderDeliveryPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/print`, data, {
    responseType: 'blob'
  })

// 供方送货单列表-空调-打印送货单校验
export const supplierOrderDeliveryPrintHtmlCheck = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/print-htmlCheck`, data)

// 供方送货单主单-供方打印 new
export const supplierOrderDeliveryPrintHtml = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/print-html`, data, {
    responseType: 'blob'
  })

// 供方-送货单列表-泛智屏-打印
export const deliverySupplierPrintApi = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/print`, data, {
    responseType: 'blob'
  })

// 供方送货单主单-供方打印 - 新模式送货单
export const newModeSupplierOrderDeliveryPrintHtml = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/supplier/print`, data, {
    responseType: 'blob'
  })

// 供方送货单主单- 供方 - 导入 - 泛智屏
export const SupplierOrderDeliveryImport = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/deliveryOrderImport`, data, {
    responseType: 'blob'
  })

// 供方送货单主单- 供方 - 导入模板下载 - 泛智屏
export const SupplierOrderDeliveryDownloadTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/exportTemplate`, data, {
    responseType: 'blob'
  })

// 供方送货单主单-采方打印
export const buyerOrderDeliveryPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/print`, data, {
    responseType: 'blob'
  })

// 供方送货单主单-采方打印
export const buyerOrderDeliveryPrintHtml = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/print-html`, data, {
    responseType: 'blob'
  })

// 采方-送货单列表-泛智屏-打印
export const deliveryBuyerPrintApi = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/buyer/print`, data, {
    responseType: 'blob'
  })

// 供方送货单主单-采方打印 - 泛智屏
export const buyerOrderDeliveryPrintHtmlTv = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/buyer/print`, data, {
    responseType: 'blob'
  })

// 供方送货单主单-补订单
export const supplierOrderDeliveryRepair = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/repair/order`, data)

// 供方送货单主单-加工商确认收货
export const supplierOrderDeliveryConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/confirm`, data, {
    timeout: 60000
  })

// 供方送货单收货-批量提交
export const batchConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/batchConfirm`, data, {
    timeout: 60000
  })

// 采方送货单收货-批量提交 new
export const batchPurConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/purConfirm`, data, {
    timeout: 60000
  })
// 送货单物流信息-获取送货单物流信息
export const getDeliveyData = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query`, data)

// 采方-送货单列表-泛智屏 明细tab根据id查询送货单
export const getDeliveyDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/buyer/getById`, data)

// 供方-送货单列表-泛智屏 明细tab根据id查询送货单
export const getSupplierDeliveyDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/getById`, data)

// 采方-送货单历史-泛智屏 明细tab根据id查询送货单
export const getHistoryDeliveyDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/archive/getBuyerById`, data)

// 供方-送货单历史-泛智屏 明细tab根据id查询送货单
export const getHistorySupplierDeliveyDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/archive/getSupplierById`, data)

// 送货单物流信息-获取送货单物流信息 - 采方 - 新模式
export const getnewModeDeliveyData = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/query/id`, data)

// 送货单物流信息-获取送货单物流信息
export const getSupplierDeliveyData = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/query`, data)

// 送货单物流信息-获取送货单物流信息 - 供方 - 新模式
export const getnewModeSupplierDeliveyData = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/query/id`, data)

// 送货单物流信息-获取送货单物流信息
export const tenantSupplierDeliveryLogisticsInfo = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierDeliveryLogisticsInfo/query/` + data)

// 送货单物流信息-获取送货单物流信息  批量
export const tenantSupplierDeliveryLogisticsInfoQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliveryLogisticsInfo/query`, data)

// 送货单物流信息-保存送货单物流信息
export const supplierDeliveryLogisticsInfoSave = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliveryLogisticsInfo/save`, data)

// 采方送货单-采方取消送货单
export const postBuyerOrderDeliveryCancel = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/cancel`, data)

// 采方送货单-采方关闭送货单
export const postBuyerOrderDeliveryClose = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/close`, data)

// 采方送货单-采方关闭送货单
export const postBuyerOrderDeliveryCloseTV = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/buyer/close`, data)

// 采方送货单-预约解锁（运维用）
export const buyerOrderDeliveryUnlock = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/releaseForecast`, data)

// 采方送货单-批量下载附件
export const postBuyerOrderDeliveryBatchDownload = (data = {}) =>
  API.post(
    `${BASE_TENANT}/delivery/header/supplier/batchDownload?screenIdFlag=${data.screenIdFlag}&supInvoiceFlag=${data.supInvoiceFlag}&hkInvoiceFlag=${data.hkInvoiceFlag}&billOfLadingFlag=${data.billOfLadingFlag}&saleOrderFlag=${data.saleOrderFlag}&deliveryScanningFlag=${data.deliveryScanningFlag}`,
    data.body,
    {
      responseType: 'blob'
    }
  )

// 采方送货单-导出
export const postBuyerOrderDeliveryExport = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/buyer/export`, data, {
    responseType: 'blob'
  })

// 采方送货单-编辑IH号
export const postUpdateIhNumber = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/updateIhNumber`, data)

// 采方送货单-采方确认收货列表
export const postBuyerOrderDeliveryQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query/item/page`, data)

// 采方送货单-采方列表
export const BuyerOrderDeliveryQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query`, data)

// 采方送货单-采方确认收货
export const postBuyerOrderDeliveryConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/confirm`, data, {
    timeout: 60000
  })

// 采方送货单-采方确认收货new
export const postNewBuyerOrderDeliveryConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/pur/batchConfirm`, data, {
    timeout: 60000
  })

// 采方 送货单列表-泛智屏 物料信息关闭
export const closeItemApi = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/item/buyer/closeItem`, data)

// 采方送货单-采方查询送货单物流信息
export const getBuyerOrderDeliveryQueryDeliveryLogisticsInfo = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerOrderDelivery/query/deliveryLogisticsInfo`, data)

// 采方送货单-采方查询供方送货单明细
export const postBuyerOrderDeliveryQueryItem = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query/item`, data)

// 供方送货司机信息维护-删除司机
export const postSupplierDriverDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/delete`, data)

// 供方送货司机信息维护-司机Excel导出
export const postSupplierDriverExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/export`, data, {
    responseType: 'blob'
  })

// 供方送货司机信息维护-导入
export const importSupplierDriverApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/import`, data, {
    responseType: 'blob'
  })

// 供方送货司机信息维护-导入模板
export const downloadSupplierDriverApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/export/template`, data, {
    responseType: 'blob'
  })

// 供方送货司机信息维护-保存司机
export const postSupplierDriverSave = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/save`, data)

// 供方送货司机信息维护-修改司机状态
export const postSupplierDriverStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/status`, data)

// 供方送货单-主单-送货单创建无订单送货单
export const postSupplierOrderDeliveryCreateNoOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/create/noOrder`, data)

// 供方送货单-导出
export const postSupplierOrderDeliveryExport = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/export`, data, {
    responseType: 'blob'
  })

// 供应商价格记录用户接口-分页查询物料价格记录详情
export const postSupplierQueryItemDetail = (data = {}) =>
  API.post(`${PROXY_PRICE_TENANT}/pricerecord/supplier/query/item/detail`, data)

// 送货地址配置-查询
export const postSiteTenantExtendQueryByEnterpriseId = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryByEnterpriseId/${data.enterpriseId}`, data.params)

// 供方-预约送货-泛智屏-导入模板
export const postSupplierForecastDeliveryExportTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/forecast/supplier/exportTemplate`, data, {
    responseType: 'blob'
  })
// 供方-预约送货-泛智屏-导入
export const postSupplierForecastDeliveryImport = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/forecast/supplier/import`, data, {
    responseType: 'blob'
  })

// 预约送货-供方-取消预约送货
export const postSupplierForecastDeliveryCancel = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/cancel`, data)

// 预约送货-供方-升级预约送货
export const postSupplierForecastDeliveryLevel = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/level`, data)

// 预约送货-供方-获取预约送货列表
export const postSupplierForecastDeliveryQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/query`, data)

// 预约送货-供方-预约送货保存
export const postSupplierForecastDeliverySave = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/save`, data)

// 预约送货-供方-提交预约送货
export const postSupplierForecastDeliverySubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/submit`, data)

// 预约送货-采方-预约送货单列表
export const postBuyerForecastDeliveryQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastDelivery/query`, data)

// 预约送货-采方-预约送货审批
export const postBuyerForecastDeliveryStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastDelivery/status?status=${data.status}`, data.body)

// 供方预约送货-预约时间段
export const getSupplierForecastDeliveryForecastTime = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierForecastDelivery/forecast/time`, data)

export const getSupplierForecastDeliveryForecastTime1 = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/forecast/timeWithVmi`, data)

// 供方送货司机信息维护-获取司机列表
export const postSupplierDriverQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/query`, data)

// 送货单列表-同步sap
export const postBuyerOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query/item/page/export`, data)

// 送货单收货 采方明细导出
export const postSupplierSyncSap = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/syncSap`, data, {
    responseType: 'blob'
  })
// 供方-送货单收货-同步
export const postProcessorReceiveSyncSap = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/processorReceiveSyncSap`, data, {
    responseType: 'blob'
  })

// 送货单收货 供方明细导出
export const postSupplierOrderDeliveryItem = (data = {}, params) =>
  API.post(`${BASE_TENANT}/supplierOrderDeliveryItem/pro/page/export`, data, {
    responseType: 'blob',
    params
  })

// 入库未定价列表-导出
export const exportNotPriced = (data = {}) =>
  API.post(`${BASE_TENANT}/po/in_out_record/exportNotPriced`, data, {
    responseType: 'blob'
  })
// 交货计划的加工方送货地址 供方取
export const siteTenantExtendQueryBySite = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryBySite`, data)
// 条码关联外部-Excel导入
export const barcodePrintExternalImport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintExternal/import`, data, {
    responseType: 'blob'
  })

// 条码关联内部-Excel导入
export const barcodePrintRecordImport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRecord/import`, data, {
    responseType: 'blob'
  })

// 条码关联内-Excel模板导出
export const barcodePrintRecordTemplateDownload = (data = {}) =>
  API.get(`${BASE_TENANT}/barcodePrintRecord/templateDownload`, data, {
    responseType: 'blob'
  })

// 条码设置导入-Excel导入
export const barcodePrintConfigImport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintConfig/import`, data, {
    responseType: 'blob'
  })

// 条码设置导入-Excel导入
export const barcodeExportTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintConfig/exportTemplate`, data, {
    responseType: 'blob'
  })

// 条码需求导入-Excel导入
export const barcodePrintRequestBarcodePrintRequestImport = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/barcodePrintRequestImport`, data, {
    responseType: 'blob'
  })

// 条码需求导入-Excel模板导出
export const barcodePrintRequestExportTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/exportTemplate`, data, {
    responseType: 'blob'
  })

// 第三方物流带出供应商
export const logisticQuerySuppliers = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi/common/logistic/querySuppliers`, data)

// 条码设置 - 获取工厂信息
export const getFactoryInfo = (data = {}) =>
  API.post(
    `/masterDataManagement/auth/site/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 条码设置 - 获取供应商信息
export const getSupplierList = (data = {}) =>
  API.post(`${BASE_TENANT}/barcode/request/getSupplierList`, data)

export const getFactoryByCode = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/supplier/findSiteBySupplierCodes`, data)

export const getSupplierInfo = (data = {}) =>
  API.post(
    'srm-purchase-execute/tenant/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier',
    data
  )

// 条码设置 - 获取列表数据
export const getBarcodeSetupTvList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/barcodePrintConfig/query-tv', data)
// 供货计划 - 供方 - 查询
export const getplanByDeliverList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/supplier/plan/query', data)

// 供货计划 - 采方 - 查询
export const getplanByDeliverProcureList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/buyer/plan/query', data)

// 供货计划 - 采方 - 屏发货指导 - 查询
export const getPlanPurScreenList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/buyer/plan/screen/query', data)

// 供货计划 - 采方 - 屏发货指导 - 查询
export const getPingcaiGroupList = (data = {}) =>
  API.post('/supplier/tenant/buyer/pingcai/group/list', data)

// 供货计划 - 供方 - 屏发货指导 - 查询
export const getPlanSupScreenList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/supplier/plan/screen/query', data)

// 供货计划 - 交货计划 - 采方 - 查询 - 泛智屏
export const getplanByDeliverProcureListTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/plan/query`, data)

// 供货计划 - 交货计划 - 采方 - 导出 - 泛智屏
export const exportPlanByDeliverProcureTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/plan/export`, data, {
    responseType: 'blob'
  })

// 供货计划 - 交货计划 - 采方 - 取消送货单 - 泛智屏
export const cancelPlanByDeliverProcureTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/plan/cancel`, data)

// 供货计划 - 采购订单 - 采方 - 查询 - 泛智屏
export const getOrderByDeliverProcureListTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/order/query`, data)

// 供货计划 - 采购订单 - 采方 - 导出 - 泛智屏
export const exportOrderByDeliverProcureTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/order/export`, data, {
    responseType: 'blob'
  })

// 供货计划 - JIT - 采方 - 查询 - 泛智屏
export const getJitByDeliverProcureListTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/jit/query`, data)

// 供货计划 - JIT - 采方 - 导出 - 泛智屏
export const exportJitByDeliverProcureTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/jit/export`, data, {
    responseType: 'blob'
  })

// 供方 - 供货计划列表Excel导出
export const postSupplierDeliverySupplyPlanExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/plan/export`, data, {
    responseType: 'blob'
  })
// 采方 - 供货计划 - 列表Excel导出
export const postProcureDeliverySupplyPlanExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/plan/export`, data, {
    responseType: 'blob'
  })
// 采方 - 屏发货指导 - 列表Excel导出
export const exportScreenPurData = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/buyer/plan/screen/export`, data, {
    responseType: 'blob'
  })
// 供方 - 屏发货指导 - 列表Excel导出
export const exportScreenSupData = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/plan/screen/export`, data, {
    responseType: 'blob'
  })
// 供方收发货供货计划-交货计划 预-创建送货单
export const postSupplierDeliverySupplyPlanPlanPreCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/plan/preCreate`, data)

// 供货计划 - 创建送货单 - 泛智屏
export const postSupplierDeliverySupplyPlanPlanCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/plan/create`, data)

// 供方 - 创建送货单 - 泛智屏 - 屏发货指导
export const postSupDeliveryPreCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/plan/screenPreCreate`, data)

// 条码需求管理 - 供方 - 导出
export const barcodePrintRequestExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRequest/export-tv`, data, {
    responseType: 'blob'
  })

// 条码打印- 供方 - 现品票 - 打印
export const barcodePrintRequestcurrentBarcodePrintTv = (data = {}) =>
  API.post(
    `${BASE_TENANT}/barcodePrintRequest/currentBarcodePrint?templateType=${data.templateType}&fontSize=${data.fontSize}&fontFamily=${data.fontFamily}`,
    data.body,
    {
      responseType: 'blob'
    }
  )

// 条码打印- 供方 - 机芯厂A4纸 - 打印
export const barcodePrintRequestcurrentBarcodePrintA4Tv = (data = {}) =>
  API.post(
    `${BASE_TENANT}/barcode/request/movementPrint?templateType=${data.templateType}`,
    data.body,
    {
      responseType: 'blob'
    }
  )
// 条码打印 - 供方 - 物料标签 - 打印
export const barcodePrintRequestitemBarcodePrintTv = (data = {}) =>
  API.post(
    `${BASE_TENANT}/barcodePrintRequest/itemBarcodePrint?fontSize=${data.fontSize}&fontFamily=${data.fontFamily}`,
    data.body,
    {
      responseType: 'blob'
    }
  )
// 条码外部关联 - 供方 - 导出
export const barcodePrintExternalExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintExternal/export-tv`, data, {
    responseType: 'blob'
  })
// 条码关联 - 供方 - 查询
export const barcodePrintRecordSearchTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintRecord/query-tv`, data)
// 条码外部关联 - 供方 - 查询
export const barcodePrintExternalSearchTv = (data = {}) =>
  API.post(`${BASE_TENANT}/barcodePrintExternal/query-tv`, data)

// 供货计划 - 供方 - 查询 - JIT
export const getJitByDeliverList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/supplier/jit/query', data)

// 供货计划 - 供方 - 查询 - 采购订单
export const getOrderByDeliverList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/supplier/order/query', data)

// 供货计划 - 采方 - 查询 - JIT
export const getJitByDeliverProcureList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/buyer/jit/query', data)

// 供货计划 - 采方 - 查询 - 采购订单
export const getOrderByDeliverProcureList = (data = {}) =>
  API.post('srm-purchase-execute/tenant/deliver/buyer/order/query', data)

// 供方收发货供货计划-订单 预-创建送货单 - 泛智屏
export const postSupplierDeliverySupplyPlanOrderPreCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/order/preCreate`, data)

// 供方收发货供货计划-订单创建送货单 - 泛智屏
export const postSupplierDeliverySupplyPlanOrderCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/order/create`, data)

// 供方收发货供货计划-JIT - 预创建送货单
export const postSupplierDeliverySupplyPlanJitPreCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/jit/preCreate`, data)

// 供货计划 - JIT - 创建送货单 - 泛智屏
export const postSupplierDeliverySupplyJitPlanCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/jit/create`, data)

// 供货计划 - 屏发货指导 - 创建送货单 - 泛智屏
export const postSupplierDeliverySupplyPlanGuideCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/plan/screenCreate`, data)

// 供方 - 供货计划列表Excel导出 - JIT
export const postSupplierDeliverySupplyJitExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/jit/export`, data, {
    responseType: 'blob'
  })

// 供方 - 供货计划列表Excel导出 - 采购订单
export const postSupplierDeliverySupplyOrderExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/deliver/supplier/order/export`, data, {
    responseType: 'blob'
  })

// 送货单列表 - 查询 - 供方 - 泛智屏
export const getDeliverListSupplierTableDataTV = (data = {}) =>
  API.post(
    `${BASE_TENANT}/delivery/header/supplier/list/page
  `,
    data
  )

// 新模式 - 送货单列表 - 查询 - 供方 - 泛智屏
export const getnewModeDeliverListSupplierTableData = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/supplier/query`, data)

// 新模式 - 送货单明细 - 查询 - 供方 - 泛智屏
export const getnewModeDeliverListSupplierTableDetailData = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/item/supplier/query`, data)

// 供方送货司机信息维护-获取司机列表 - 供方 - 泛智屏
export const postSupplierDriverQueryTv = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDriver/query`, data)

// 预约送货-供方-获取预约信息列表
export const getSupplierForecastDeliveryList = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/query-tv`, data)

// 预约送货-供方-获取 ASN/VMI 入库单
export const getASNAndVMIListInfo = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/pageForForecast-tv`, data)

// 预约送货-供方-获取 ASN 入库单
export const getASNListInfo = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/query/forecast-tv`, data)

// 送货单列表 - 获取列表明细 - 采方
export const DeliverListTAbleDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/query-tv`, data)

// 送货单列表 - 获取列表明细 - 采方 - 新模式
export const newModeDeliverListTAbleDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/buyer/query`, data)

// 送货单列表 - 获取列表明细 - 采方 - 新模式
export const newModeDeliverListTAbleDetailDataTV = (data = {}) =>
  API.post(`${BASE_TENANT}/newMode/delivery/item/buyer/query`, data)

// 无PO送货申请 - 供方 - 泛智屏
export const getNopoDeliveryRequestListTV = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/supplier/query`, data)

// 无PO送货申请 - 采方 - 泛智屏
export const getNopoDeliveryRequestPurChaseListTV = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/buyer/query`, data)

// 创建无PO送货申请单 - 供方 - 泛智屏
export const createNoPoDeliveryRequestDocument = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/supplier/submit`, data)

// 创建无PO送货申请单 - 采方 - 泛智屏
export const createNoPoDeliveryRequestPurChaseDocument = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/buyer/submit`, data)

// 保存无PO送货申请单 - 供方 - 泛智屏
export const saveNoPoDeliveryRequestDocument = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/supplier/save`, data)

// 保存无PO送货申请单 - 采方 - 泛智屏
export const saveNoPoDeliveryRequestPurChaseDocument = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/buyer/save`, data)

// 获取库存地址 - 供方 - 泛智屏
// export const getSiteCodeAddr = (data = {}) =>
//   API.post(`${BASE_TENANT}/siteTenantExtend/queryBySiteCode`, data)
export const getSiteCodeAddr = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/distinct/queryBySiteCode`, data)

// 无PO送货申请单 - 获取工作中心
export const getWorkCenterBySite = (data = {}) =>
  API.post(`/masterDataManagement/common/work-center/query`, data)

// 获取单位 - 供方 - 泛智屏
export const getUnitList = (data = {}) => API.post(`${PROXY_MDM_COMMON_TENANT}/unit/findUnit`, data)

// 无PO送货申请批量提交 - 供方 - 泛智屏
export const batchConfirmNoPo = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/supplier/batchSubmit`, data)

// 无PO送货申请批量提交 - 采方 - 泛智屏
export const batchConfirmNoPoPurChase = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/buyer/batchSubmit`, data)

// 无PO送货单申请批量删除 - 供方 - 泛智屏
export const batchDeleteNoPo = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/supplier/delete`, data)

// 无PO送货单申请批量删除 - 采方 - 泛智屏
export const batchDeleteNoPoPurChase = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/buyer/delete`, data)

// 无PO送货单申请批量删除 - 采方 - 泛智屏
export const batchCloseNoPoPurChase = (data = {}) =>
  API.post(`${BASE_TENANT}/noOrderDeliveryApply/buyer/batchClose`, data)

// 送货单维护订单 - 查询 - 供方 - 泛智屏
export const getSupplierOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/supplier/queryNoOrderDelivery`, data)

// 送货单维护订单 - 自动补单查询 - 供方 - 泛智屏
export const preReplenishOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/supplier/preReplenishOrder`, data)

// 送货单维护订单 - 查询 - 采方 - 泛智屏
export const getPurChaseOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/buyer/queryNoOrderDelivery`, data)

// 送货单维护订单 - 导出 - 供方 - 泛智屏
export const exportSupplierOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/supplier/export`, data, {
    responseType: 'blob'
  })

// 送货单维护订单 - 导出 - 采方 - 泛智屏
export const exportPurChaseOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/buyer/export`, data, {
    responseType: 'blob'
  })

// 送货单维护订单 - 保存 - 供方 - 泛智屏
export const saveSupplierOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/supplier/save`, data)

// 送货单维护订单 - 保存 - 采方 - 泛智屏
export const savePurChaseOrderDelivery = (data = {}) =>
  API.post(`${BASE_TENANT}/replenish/buyer/save`, data)

// 采供方车辆物流信息链接
export const purQueryVehicleLogistics = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/forecast/getTmsCarPositionUrl`, data)

// 采供方预约送货同步按钮
export const purforecastSync = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastDelivery/forecast/sync`, data)

// 采方预约送货取消按钮
export const cancelBuyerForecastDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastDelivery/cancel`, data)

// 采方预约送货 - 导出
export const exportBuyerForecastDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastDelivery/export`, data, {
    responseType: 'blob'
  })

// 采方预约送货 - 青白江监管账册资料下载
export const downloadBuyerForecastDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastDelivery/accountBook/download`, data, {
    responseType: 'blob'
  })

// 供方预约送货 - 导出
export const exportSupplierForecastDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierForecastDelivery/export`, data, {
    responseType: 'blob'
  })

// 供方预约送货 - 空调打印送货单
export const ktPrintSupplierForecastDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/forecastDelivery/ktPrint`, data, {
    responseType: 'blob'
  })

// 供方预约送货 - 白电打印送货单
export const bdPrintSupplierForecastDeliveryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/forecastDelivery/bdPrint`, data, {
    responseType: 'blob'
  })

// 供方送货单列表附件保存
export const supDeliveryFileSave = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/files/saveBatch`, data)

// 供方送货单列表附件删除
export const supDeliveryFileDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/tv/files/delete/file`, data)

// 供方送货单主单- 供方 - 导入 - 泛智屏
export const supDeliveryImport = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/importScreen`, data, {
    responseType: 'blob'
  })

// 供方送货单主单- 供方 - 导入模板下载 - 泛智屏
export const supDeliveryDownloadTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/exportScreenTemplate`, data, {
    responseType: 'blob'
  })
// 供方-SAP库存查询-库位选择
export const getPositionOptionsApi = (data = {}) =>
  API.get(`${BASE_TENANT}/tvOutSouring/querySupplierLocation`, data)

// 供方-批量创建送货单-批量修改-根据工厂数组获取收货地址
export const getAddressBySiteCodesApi = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryBySiteCodes`, data)

// 供方-批量创建送货单-批量修改-根据工厂数组获取收货地址
export const getWorkCenterBySiteCodesApi = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/getWorkCenterCodeBySiteCodes`, data)

// 采方-报关要素资料-分页查询
export const pageCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/customs/declaration/pageQuery`, data)

// 采方-报关要素资料-导出
export const exportCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/customs/declaration/export`, data, {
    responseType: 'blob'
  })

// 供方-报关要素资料-分页查询
export const pageSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/pageQuery`, data)
// 供方-报关要素资料-保存
export const saveSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/save`, data)
// 供方-报关要素资料-删除
export const deleteSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/delete`, data)
// 供方-报关要素资料-同步Tms
export const syncTmsSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/syncTms`, data)
// 供方-报关要素资料-导入模板
export const tempSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/downTemplate`, data, {
    responseType: 'blob'
  })
// 供方-报关要素资料-导入
export const importSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/import`, data, {
    responseType: 'blob'
  })
// 供方-报关要素资料-导出
export const exportSupplierCustomsDeclarationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/customs/declaration/export`, data, {
    responseType: 'blob'
  })
// 采方- 海外收货差异报表 - 查询
export const queryOverseasReceiptsDiff = (data = {}) =>
  API.post(`/statistics/tenant/delivery/report/overseas/delivery/diff`, data)

// 采方- 海外收货差异报表 - 导出
export const exportOverseasReceiptsDiff = (data = {}) =>
  API.post(`/statistics/tenant/delivery/report/overseas/delivery/diff/excel/export`, data, {
    responseType: 'blob'
  })

// ----------------------------采供-供方系统送货单------------------------------
export const pageOrderDeliveryTiApi = (data = {}) =>
  API.post(`${BASE_TENANT}/OrderDeliveryTi/query`, data)

export const exportOrderDeliveryTiApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/OrderDeliveryTi/export`, data, {
    responseType: 'blob'
  })
}

export const pageOrderDeliveryTiDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/OrderDeliveryTi/queryDetail`, data)

export const exportOrderDeliveryTiDetailApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/OrderDeliveryTi/exportDetail`, data, {
    responseType: 'blob'
  })
}

// 手工获取
export const pullOrderDeliveryTiApi = (data = {}) =>
  API.get(`${BASE_TENANT}/OrderDeliveryTi/pull`, data)

export const createOrderDeliveryTiApi = (data = {}) =>
  API.post(`${BASE_TENANT}/OrderDeliveryTi/createDelivery`, data)

// -------------------------------供方-报关要素（新）----------------------------------
// 供方-报关要素资料-分页查询
export const pageCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/queryPage`, data)
// 供方-报关要素资料-新增
export const addCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/add`, data)
// 供方-报关要素资料-编辑
export const editCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/edit`, data)
// 供方-报关要素资料-删除
export const deleteCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/delete`, data)
// 供方-报关要素资料-提交
export const submitCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/submit`, data)
// 供方-报关要素资料-导入模板
export const downloadCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.get(`/contract/tenant/declareCustoms/importTemplateDowload`, data, {
    responseType: 'blob'
  })
// 供方-报关要素资料-导入校验
export const importValidCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/validImportExcel`, data, {
    responseType: 'blob'
  })
// 供方-报关要素资料-导入
export const importCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/import`, data, {
    responseType: 'blob'
  })
// 供方-报关要素资料-导出
export const exportCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/export`, data, {
    responseType: 'blob'
  })
// 供方-报关要素资料-图片批量上传
export const batchUploadCustomsDeclarationElementsSupNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/supplier/batchPicUpload`, data)
// -------------------------------采方-报关要素（新）----------------------------------
// 采方-报关要素资料-分页查询
export const pageCustomsDeclarationElementsNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/buyer/queryPage`, data)
// 采方-报关要素资料-审批通过
export const passCustomsDeclarationElementsNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/buyer/pass`, data)
// 采方-报关要素资料-审批驳回
export const rejectCustomsDeclarationElementsNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/buyer/reject`, data)
// 采方-报关要素资料-导出
export const exportCustomsDeclarationElementsNewApi = (data = {}) =>
  API.post(`/contract/tenant/declareCustoms/buyer/export`, data, {
    responseType: 'blob'
  })

// 采供-报关要素资料-附件
export const pageFileCustomsDeclarationElementsNewApi = (data = {}) =>
  API.get(`/contract/tenant/tv/files/query/list`, data)
// 采供-报关要素资料-附件上传
export const saveFileCustomsDeclarationElementsNewApi = (data = {}) =>
  API.post(`/contract/tenant/tv/files/saveBatch`, data)
// 采供-报关要素资料-附件删除
export const deleteFileCustomsDeclarationElementsNewApi = (data = {}) =>
  API.delete(`/contract/tenant/tv/files/delete/file`, data)
// 供方-报关要素资料-批量上传
export const saveBatchFileCustomsDeclarationElementsNewApi = (data = {}) =>
  API.post(`/contract/tenant/tv/files/declareCustoms/saveBatch`, data)

// -------------------------------采供-蕴鑫收货报表----------------------------------
// 采方-蕴鑫收货报表-分页查询
export const pageReceiptAndDeliveryApi = (data = {}) =>
  API.post(`/contract/tenant/yxReceiptReport/purchasePageQuery`, data)
// 采方-蕴鑫收货报表-导出
export const exportReceiptAndDeliveryApi = (data = {}) =>
  API.post(`/contract/tenant/yxReceiptReport/purchaseExport`, data, {
    responseType: 'blob'
  })
// 供方-蕴鑫收货报表-分页查询
export const pageReceiptAndDeliverySupApi = (data = {}) =>
  API.post(`/contract/tenant/yxReceiptReport/supplierPageQuery`, data)
// 供方-蕴鑫收货报表-导出
export const exportReceiptAndDeliverySupApi = (data = {}) =>
  API.post(`/contract/tenant/yxReceiptReport/supplierExport`, data, {
    responseType: 'blob'
  })
