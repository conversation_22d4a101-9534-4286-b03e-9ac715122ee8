import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'invoiceCollaboration'

// 保存文件信息
export const putReconciliationHeaderSaveFile = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/saveFile`, data)

// 保存文件信息 销售价格维护
export const putSalePriceSaveFile = (data = {}) =>
  API.put(`${BASE_TENANT}/selling_price/saveFile`, data)

// 提交对账发票信息
export const putReconciliationInvoiceCommitInvoice = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationInvoice/commitInvoice`, data)

// 发票计算
export const putReconciliationInvoiceInvoiceCalculation = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationInvoice/invoiceCalculation`, data)

// 税控平台获取数据
export const postInvoiceGetInvoiceFromItbc = (data = {}) =>
  API.post(`${BASE_TENANT}/external/reconciliation/invoice/getInvoiceFromItbc`, data)

// 查询对账发票信息
export const getReconciliationInvoiceQueryList = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliationInvoice/queryList`, data)

// 根据对账单ID确认发票
export const postReconciliationInvoiceConfirmById = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationInvoice/confirmById`, data)

// 根据对账单ID退回发票
export const getReconciliationInvoiceReturnById = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliationInvoice/returnById`, data)

// 采方发票下载（导出）（导入的文件模板）
export const postDownloadInvoice = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationInvoice/downloadInvoice`, data, {
    responseType: 'blob'
  })

// 采方发票模板上传（导入）
export const postReconciliationInvoiceUpload = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationInvoice/upload`, data, {
    responseType: 'blob'
  })

// 对账单外部接口-对账单推送第三方共享财务
export const postReconciliationPushSharedFinance = (data = {}) =>
  API.post(`${BASE_TENANT}/external/reconciliation/pushSharedFinance`, data)

// 对账单主单接口-对账单推送SAP
export const postReconciliationHeaderPushSAP = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/pushSAP`, data)

// 租户级-供方对账单发票信息-v2接口-查询附件列表
export const postReconciliationInvoiceV2FileList = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/file-list`, data)

// 租户级-供方对账单发票信息-v2接口-新增
export const postReconciliationInvoiceV2Add = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/add`, data)
// 租户级-采方对账单发票信息-v2接口-新增
export const postBuyReconciliationInvoiceV2Add = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/uploadInvoiceV2`, data)
// 租户级-供方对账单发票信息-非采接口-新增
export const postReconciliationInvoiceSpecialAdd = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/specialAdd`, data)
// 租户级-供方对账单发票信息-v2接口-批量新增
export const postReconciliationInvoiceV2BatchAdd = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/batch-add`, data)

// 租户级-供方对账单发票信息-v2接口-更新
export const putReconciliationInvoiceV2Update = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliation-invoice-v2/update`, data)

// 租户级-供方对账单发票信息-v2接口-更新
export const putReconciliationInvoiceTVUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/update`, data)

// 租户级-供方对账单发票信息-v2接口-批量删除
export const deleteReconciliationInvoiceV2BatchDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/reconciliation-invoice-v2/batch-delete`, data)

// 租户级-供方对账单发票信息-v2接口-批量关闭
export const deleteReconciliationInvoiceV2BatchClose = (data = {}) =>
  API.delete(`${BASE_TENANT}/reconciliation-invoice-v2/batch-close`, data)

// 租户级-采方清账发票信息-v2接口-关闭
export const clearInvoiceV2BatchClose = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/closeById`, data)

// 租户级-供方对账单发票信息-v2接口-批量提交
export const postReconciliationInvoiceV2BatchSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/batch-submit`, data)

// 租户级-采方对账单发票信息-v2接口-提交
export const postBuyReconciliationInvoiceV2SubmitByHeader = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/pur-submit-invoice`, data)
// 租户级-供方对账单发票信息-v2接口-提交
export const postReconciliationInvoiceV2SubmitByHeader = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/submit-by-header`, data)
export const postReconciliationInvoiceSpecailSubmitByHeader = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/submit-specail-header`, data)

// 租户级-供方对账单发票信息-v2接口-提交
export const postReconciliationInvoiceV2SubmitByHeaders = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/submit-by-headers`, data)

// 租户级-发票协同-供方主单-对账信息
export const postSupplierReconciliationHeaderQueryBuilder = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/reconciliationHeader/queryBuilder`, data)

// 租户级-发票协同-供方-数据导入
export const postReconciliationInvoiceV2DataImport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/data-import`, data, {
    responseType: 'blob'
  })

// 租户级-发票协同-供方-导出
export const postReconciliationInvoiceV2Export = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/export`, data, {
    responseType: 'blob'
  })

// 租户级-发票协同-采方-导出
export const postCustomerReconciliationInvoiceV2Export = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/export`, data, {
    responseType: 'blob'
  })
// 租户级-发票协同列表-采方-导出
export const postInvoiceExport = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/exportInvoiceV2`, data, {
    responseType: 'blob'
  })
// 租户级-清账发票处理-发票列表-导出
export const postBuyInvoiceExport = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/exportSettledInvoiceV2`, data, {
    responseType: 'blob'
  })
// 租户级-发票协同列表-采方-明细-导出
export const postInvoiceDetailExport = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/specialExportInvoiceV2`, data, {
    responseType: 'blob'
  })
// 租户级-发票协同列表-供方-导出
export const postSupplierInvoiceExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/reconciliationHeader/exportInvoiceV2`, data, {
    responseType: 'blob'
  })
// 租户级-发票协同-采方主单-对账信息
export const postReconciliationHeaderQueryBuilder = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/queryBuilder`, data)

// 采方财务确认
export const postCustomerReconciliationInvoiceV2FinanceConfirm = (data = {}) =>
  API.post(
    `${BASE_TENANT}/customer-reconciliation-invoice-v2/finance-confirm?BU_CODE=${data.currentBu}`,
    data
  )

// 采方采购确认
export const postCustomerReconciliationInvoiceV2purchaserConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/purchaser-confirm`, data)

// 租户级-发票协同-采方-对账单推送第三方共享财务
export const postCustomerReconciliationInvoiceV2PushSharedFinance = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/push-shared-finance`, data)

export const exportInvoiceSummaryApi = (data = {}) =>
  API.post(
    `${BASE_TENANT}/customer-reconciliation-invoice-v2/customer-purchaser-exportInvoiceV2`,
    data,
    {
      responseType: 'blob'
    }
  )

// 采方-采购发票查询-手工驳回
export const updateReconciliationStatusApi = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/updateReconciliationStatus`, data)

// 租户级-发票协同-采方-发票推送预制发票
export const postPushPreMakeInvoice = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/push-premake-invoice`, data)

// 租户级-发票协同-采方-发票推送预制发票
export const postPushPreMadInvoice = (data = {}) =>
  API.post(
    `${BASE_TENANT}/customer-reconciliation-invoice-v2/push-preMad-invoice?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

// 非采对账单推送第三方共享财务-携带员工信息
export const postPushSharedFinanceCarryEmployee = (data = {}) =>
  API.post(
    `${BASE_TENANT}/customer-reconciliation-invoice-v2/push-shared-finance-carry-employee`,
    data
  )

// 租户级-新增发票-供方-导入发票 - tv
export const invoiceImportSup = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/invoiceImport`, data, {
    responseType: 'blob'
  })

// 租户级-新增发票-供方-导入发票模板 - tv
export const downloadInvoiceTemplateSup = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/downloadInvoiceTemplate`, data, {
    responseType: 'blob'
  })

// 租户级-新增发票-供方-保存发票 - tv
export const batchSaveOrUpdateInvoiceSup = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/batchSaveOrUpdate`, data)

// 租户级-发票协同查询-供方-提交发票 - tv
export const submitInvoiceSup = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/common/submit`, data)

// 租户级-发票协同-采方-查询附件列表
export const postCustomerReconciliationInvoiceV2FileList = (data = {}) =>
  API.post(`${BASE_TENANT}/customer-reconciliation-invoice-v2/file-list`, data)

// 租户级-发票协同-获取待对账客户名称
export const getSupplierReconciliationHeaderGetCustomerTenant = (data = {}) =>
  API.get(`${BASE_TENANT}/supplier/reconciliationHeader/getCustomerTenant`, data)

export const getReconcliationSupplierName = (data = {}) =>
  API.get(`${BASE_TENANT}/supplier/reconciliationHeader/getSupplierName`, data)

// 获取客户列表及客户的对账类型
export const getCustomerReconciliationType = (data = {}) =>
  API.get(`${BASE_TENANT}/supplier/reconciliationHeader/getCustomerReconciliationType`, data)

// 租户级-发票协同-供方-根据主单查询采方的字典
export const postSupplierDictByHeader = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/dict-by-header`, data)

// 租户级-发票协同-采方-根据主单查询采方的字典
export const postDictByHeader = (data = {}) =>
  API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)

//根据id查询对账明细
export const getSupplierReconciliationItemById = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationItemSupplier/queryBuilder`, data)

//根据id查询对账明细 采方
export const getReconciliationItemById = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationItem/queryBuilder?BU_CODE=${data.currentBu}`, data)

// 采购发票处理 - 可开票明细 - 查询 - 泛智屏
export const getInvoiceSupplierList = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/pagedQuery`, data)

// 采购发票处理 - 可开票单据 - 查询 - 泛智屏
export const getInvoiceSupplierHeaderList = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/header`, data)

// 采购发票处理 - 供方提交发票 - 泛智屏
export const submitInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/submit`, data)

// 采购发票处理 - 供方上传发票 - 泛智屏
export const uploadInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/upload`, data)

// 采购发票处理 - 供应商导入发票 - 泛智屏
export const importInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/import`, data, {
    responseType: 'blob'
  })

// 采购发票处理 - 供应商删除发票 - 泛智屏
export const deleteInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/supplier/delete`, data)

// 采购发票处理 - 对账单发票推送SAP - 泛智屏
export const pushToSapApi = (data = {}) => API.post(`${BASE_TENANT}/tv/invoice/pushToSap`, data)

// 采购发票处理 - 采购确认或者退回发票 - 泛智屏
export const confirmInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/purchaser/confirm`, data)

// 采购发票处理 - 发票确认、驳回 - 泛智屏 - new 针对没有单据号的数据
export const confirmInvoiceNoOrderNumSup = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/common/confirm`, data)

// 采购发票处理 - 推送SAP - 泛智屏 - new 针对没有单据号的数据
export const pushInvoiceNoOrderNumSup = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/commonPushToSap`, data)

// 采购发票处理 - 对账单发票打印 - 泛智屏
export const printInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/print`, data, {
    responseType: 'blob'
  })

// 采购发票处理 - 对账单发票导出 - 泛智屏
export const exportInvoiceSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/export/headerInvoice`, data, {
    responseType: 'blob'
  })

// 采购发票处理 - 对账单发票导出 - 泛智屏
export const exportInvoice = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/invoiceExport`, data, {
    responseType: 'blob'
  })

// 采购发票处理 - 查询发票附件列表 - 泛智屏
export const queryInvoiceFileList = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/file/list`, data)

// 采购发票处理 - 对账单发票导出 - 泛智屏
export const exportInvoiceList = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/export`, data, {
    responseType: 'blob'
  })

// 采购发票处理 - 根据对账单ID查询发票列表 - 泛智屏
export const queryInvoiceDetailList = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/detail/list`, data)

// 采购发票列表查询 - 泛智屏
export const queryPurchaseDetailList = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/invoice/header/pagedQuery`, data)

// 采购发票确认详情查询 - 泛智屏
export const queryPurchaseDetailTV = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/query/common`, data)

// ----------------------------采方-供方系统发票查询------------------------------
export const pageSupplierInvoiceInquiryListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoice/buyer/queryPage`, data)

export const exportSupplierInvoiceInquiryListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoice/buyer/export`, data, {
    responseType: 'blob'
  })

export const pageSupplierInvoiceInquiryDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoiceDetail/buyer/queryPage`, data)

export const exportSupplierInvoiceInquiryDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoiceDetail/buyer/export`, data, {
    responseType: 'blob'
  })

// 供方系统发票查询 - 手动获取
export const pullSupplierInvoiceInquiryApi = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierSystemInvoice/handPull`, data)

// ----------------------------供方-本方系统发票查询------------------------------
export const pageInvoiceInquiryListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoice/supplier/queryPage`, data)

export const exportInvoiceInquiryListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoice/supplier/export`, data, {
    responseType: 'blob'
  })

export const pageInvoiceInquiryDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoiceDetail/supplier/queryPage`, data)

export const exportInvoiceInquiryDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSystemInvoiceDetail/supplier/export`, data, {
    responseType: 'blob'
  })

// 本方系统发票查询 - 手动获取
export const pullInvoiceInquiryApi = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierSystemInvoiceDetail/handPull`, data)

// ----------------------------采方-汇款通知------------------------------
export const pageRemittanceAdviceApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/buyer/queryPage`, data)

export const saveRemittanceAdviceApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/save`, data)

export const deleteRemittanceAdviceApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/delete`, data)

export const downloadRemittanceAdviceApi = (data = {}) => {
  return API.get(`${BASE_TENANT}/remittanceAdvice/downloadTemplate`, data, {
    responseType: 'blob'
  })
}

export const importRemittanceAdviceApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/remittanceAdvice/import`, data, {
    responseType: 'blob'
  })
}

export const exportRemittanceAdviceApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/buyer/export`, data, {
    responseType: 'blob'
  })

export const syncRemittanceAdviceApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/syncSupplierSystem`, data)

// ----------------------------采方-汇款通知------------------------------
export const pageRemittanceAdviceSupplierApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/supplier/queryPage`, data)

export const exportRemittanceAdviceSupplierApi = (data = {}) =>
  API.post(`${BASE_TENANT}/remittanceAdvice/supplier/export`, data, {
    responseType: 'blob'
  })

// ----------------------------采供-外发发票------------------------------
// 供方-外发发票
export const headerExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/header`, data)
// 根据对账单ID查询发票清单
export const listExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/detail/list`, data)
// 供方-发票清单-新增
export const addExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/upload`, data)
// 供方-发票清单-修改
export const editExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/update`, data)
// 供方-发票清单-删除
export const deleteExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/delete`, data)
// 供方-发票清单-发票导入模板下载
export const downloadExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/download/template`, data, {
    responseType: 'blob'
  })
// 供方-发票清单-发票导入
export const importExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/import`, data, {
    responseType: 'blob'
  })
// 供方-发票清单-提交
export const submitExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/submit`, data)
// 采方-发票清单-导出
export const exportExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/invoiceExport`, data, {
    responseType: 'blob'
  })

// 采方-采购发票确认-外发发票-采购确认或者退回发票
export const confirmExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/purchaser/confirm`, data)
// 采方-采购发票确认-外发发票-推送SAP
export const pushSapExternalInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/pushToSap`, data)

// ----------------------------供方-越南日结对账发票------------------------------
export const headerVnInvoiceApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/supplier/daily/header`, data)

// ----------------------------采方-越南发票报表---------------------------------
// 标准-分页
export const pageVnInvoiceReportStandardApi = (data = {}) =>
  API.post(`/contract/tenant/daily/standard/reconciliation/item/report/list`, data)
// 标注-推送SAP
export const pushSapVnInvoiceReportStandardApi = (data = {}) =>
  API.post(`/contract/tenant/daily/standard/reconciliation/item/push/report`, data)
// 寄售-分页
export const pageVnInvoiceReportConsignmentApi = (data = {}) =>
  API.post(`/contract/tenant/daily/consignment/reconciliation/item/report/list`, data)
// 寄售-推送SAP
export const pushSapVnInvoiceReportConsignmentApi = (data = {}) =>
  API.post(`/contract/tenant/daily/consignment/reconciliation/item/push/report`, data)

// 财务退回sap
export const financeBackSap = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/customer-reconciliation-invoice-v2/financeBackSap`, data)

// 采购申请退回sap
export const purchaseBackSap = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/customer-reconciliation-invoice-v2/purchaseBackSap`, data)
