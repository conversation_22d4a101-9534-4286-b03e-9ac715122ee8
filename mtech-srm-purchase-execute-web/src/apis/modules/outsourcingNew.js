/* eslint-disable prettier/prettier */
import { API } from '@mtech-common/http'
import { BASE_TENANT, PROXY_MDM_TENANT, PROXY_MDM_AUTH } from '@/utils/constant'
export const NAME = 'outsourcingNew'

export const queryCustomCompanyList = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/queryPermissionCompanyList`, data)

// 根据公司id - 获取工厂下拉枚举集
export const companyGetsiteList = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 根据公司编码 - 获取供应商下拉枚举集
export const companyCodeGetSupplierList = (data = {}) =>
  API.post(`${PROXY_MDM_AUTH}/supplier/queryByCustomerCode`, data)

// 根据调入供应商 - 获取送货地址
export const InSupplierGetAddressList = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryBySupplierForSteel`, data)

// 委外调拨管理 - 物料信息 - 物料下拉枚举集
export const getitemCodeList = (data = {}) =>
  API.post(`${PROXY_MDM_AUTH}/item-org-rel/pur-paged-query`, data)

// 调拨 委外需求量
export const OutQuerySapOutDemand = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/supplier/querySapOutDemand`, data)

// 根据公司企业ID,工厂编码  获取库存地点
export const getWarehouseList = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryByEnterpriseId/${data.id}`, data.param)

// 销售委外倒挂额度 - 额度查询
export const getSalesOutsourcingMoney = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutsourceAmountConfig/queryPage`, data)

// 销售委外倒挂额度 - 额度查询 - 导出
export const exportSalesOutsourcingMoney = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutsourceAmountConfig/export`, data, {
    responseType: 'blob'
  })

// 销售委外倒挂额度 - 申请表查询
export const getApplicationFormQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApply/queryPage`, data)

// 销售委外倒挂额度 - 申请表 - 关闭
export const cancelApplicationForm = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApply/cancel`, data)

// 销售委外倒挂额度 - 申请表 - 新增 - 提交
export const addApplicationForm = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApply/submit`, data)

// 销售委外倒挂额度 - 申请表 - 新增 - 保存
export const saveApplicationForm = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApply/save`, data)

// 销售委外倒挂额度 - 申请表 - 明细表查询
export const getApplicationReportDetails = (data = {}) =>
  API.get(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/queryItemList/${data.applyNo}`)

// 销售委外倒挂额度 - 申请表 - 抬头信息查询
export const getApplicationFormDetails = (data = {}) =>
  API.get(`${BASE_TENANT}/saleOutSourceAmountApply/queryDetail/${data.id}`)

// 销售委外倒挂额度 - 申请表 - 抬头 - 获取申请单号
export const getApplyNoInfo = () => API.get(`${BASE_TENANT}/saleOutSourceAmountApply/getApplyNo`)

// 销售委外倒挂额度 - 申请表 - 明细表 - 保存
export const saveApplicationFormDetails = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/saveItem`, data)

// 销售委外倒挂额度 - 申请表 - 明细表 - 导入
export const importApplicationFormDetails = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/importItem`, data, {
    responseType: 'blob'
  })

// 销售委外倒挂额度 - 申请表 - 明细表 - 模板下载 & 导出
export const downloadTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/exportItem`, data, {
    responseType: 'blob'
  })

// 销售委外倒挂额度 - 申请表 - 明细表 - 删除
export const deleteApplicationFormDetails = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/deleteItem`, data)

// 销售委外倒挂额度 - 申请表明细查询
export const getApplicationFormDetailQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/queryPage`, data)

// 销售委外倒挂额度 - 申请表明细查询 - 导出
export const exportApplicationFormDetailQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutSourceAmountApplyDetail/export`, data, {
    responseType: 'blob'
  })

// 获取共享组织
export const getOrgCodeList = () =>
  API.get(`${BASE_TENANT}/saleOutsourceAmountConfig/getShareOrgList`)

// 根据工厂获取共享组织
export const getSiteCodeToOrgCode = ({ siteCode = '' }) =>
  API.get(`${BASE_TENANT}/saleOutsourceAmountConfig/getShareOrgBySite/${siteCode}`)

// 根据共享组织,供应商,客户编码获取倒挂金额
export const getSiteCodeToOrgCodeAmt = (data = {}) =>
  API.get(
    `${BASE_TENANT}/saleOutsourceAmountConfig/querySaleOutsourceAmountConfig/${data.orgCode}/${data.supplierCode}`
  )

// 销售委外倒挂金额 - 额度申请单 - 获取上传文件
export const getFilelistInfo = (data = {}) =>
  API.get(`${BASE_TENANT}/saleOutSourceAmountApply/queryFile/${data.id}`)

// 销售委外倒挂金额 - 额度申请单 - 上传附件
export const deleteHeaderInfo = (data = {}) =>
  API.delete(`${BASE_TENANT}/saleOutSourceAmountApply/delete`, data)

// 供方-查物料
export const getItemSup = (data = {}) =>
  API.post(
    `/masterDataManagement/tenant/item/paged-auth?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
