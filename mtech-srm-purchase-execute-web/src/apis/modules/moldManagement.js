import { API } from '@mtech-common/http'

export const NAME = 'moldManagement'

// 审厂考核模板-分页查询
export const pageReviewAssessmentTemplateApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/template/query`, data)
// 审厂考核模板-保存
export const saveReviewAssessmentTemplateApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/template/saveOrUpData`, data)
// 审厂考核模板-删除
export const deleteReviewAssessmentTemplateApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/template/delete`, data)
// 审厂考核模板-生效
export const enableReviewAssessmentTemplateApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/template/effective`, data)
// 审厂考核模板-失效
export const disableReviewAssessmentTemplateApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/template/expire`, data)
// 审厂考核模板-查询明细
export const detailReviewAssessmentTemplateApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/template/queryDetailById`, data)

// -----------------------------------------------------------------------------

// 审厂管理-分页查询
export const pageFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/buyerQueryPage`, data)
// 审厂管理-保存
export const saveFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/save`, data)
// 审厂管理-删除
export const deleteFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/delete`, data)
// 审厂管理-导出
export const exportFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/buyerExport`, data, {
    responseType: 'blob'
  })
// 审厂管理-获取详情
export const detailFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/detail`, data)

// 审厂管理-部门确认
export const deptConfirmApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/deptConfirm`, data)

// 审厂管理-获取模具工厂下拉列表
export const queryListApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/supplier/queryMouldFactorySupplierList`, data)

// 审厂管理-获取审厂模板下拉列表
export const queryTemplateListApi = (data = {}) =>
  API.post(`/contract/tenant/mouldFactoryExamineScore/queryTemplateList`, data)

// 审厂管理-根据模板id，查询考核部门下拉框
export const queryExamineDepartmentListApi = (data = {}) =>
  API.post(`/contract/tenant/mouldFactoryExamineScore/queryExamineDepartmentList`, data)

// 审厂管理-模具审厂打分-获取详情
export const getScoreDetailApi = (data = {}) =>
  API.post(`/contract/tenant/mouldFactoryExamineScore/getScoreDetail`, data)

// 审厂管理-模具审厂打分-保存
export const saveScoreApi = (data = {}) =>
  API.post(`/contract/tenant/mouldFactoryExamineScore/saveScore`, data)

// 审厂管理-打分审核-通过/驳回
export const scoreApprovalApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/scoreApproval`, data)

// 审厂管理-供方-分页查询
export const pageFactoryAuditManagementSupApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/supplierQueryPage`, data)
// 审厂管理-供方-导出
export const exportFactoryAuditManagementSupApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/supplierExport`, data, {
    responseType: 'blob'
  })
// 审厂管理-供方-获取供方申诉
export const supplierQueryResultAppealApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/supplierQueryResultAppeal`, data)
// 审厂管理-供方-提交供方结果申诉
export const supplierSubmitResultAppealApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/supplierSubmitResultAppeal`, data)
// 审厂管理-采方-获取供方申诉
export const buyerQueryResultAppealApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/buyerQueryResultAppeal`, data)
// 审厂管理-采方-提交供方结果申诉
export const buyerSubmitResultAppealApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/buyerSubmitResultAppeal`, data)
// 审厂管理-整改审核
export const rectificationApprovalApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamine/rectificationApproval`, data)
// 审厂管理-查询内容变更记录
export const pageLogFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamineLog/queryPage`, data)
// 审厂管理-导出内容变更记录
export const exportLogFactoryAuditManagementApi = (data = {}) =>
  API.post(`/contract/tenant/mould/factoryExamineLog/export`, data, {
    responseType: 'blob'
  })
// 审厂管理-采方-OA审批查看
export const queryOaViewApi = (data = {}) =>
  API.post(`/contract/public-api/mould/factoryExamine/queryOaView`, data)

// -----------------------------------------------------------------------------
// 日志查询
export const getTemplateLogApi = (data = {}) =>
  API.post(`/contract/tenant/mould/reviewFactory/templateLog/query`, data)

// -----------------------------------------------------------------------------
// 交货数统计-采方-分页查询
export const pageDeliveryQuantityStatisticsApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/query`, data)
// 交货数统计-供方-分页查询
export const pageDeliveryQuantityStatisticsSupApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/supplierQuery`, data)
// 交货数统计-供方-保存
export const saveDeliveryQuantityStatisticsApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/saveOrUpData`, data)
// 交货数统计-供方-删除
export const deleteDeliveryQuantityStatisticsApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/delete`, data)
// 交货数统计-供方-下载模板
export const downloadDeliveryQuantityStatisticsApi = (data = {}) =>
  API.get(`/contract/tenant/mould/delivery/schedule/excelImportTemplate`, data, {
    responseType: 'blob'
  })
// 交货数统计-供方-导入
export const importDeliveryQuantityStatisticsApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/importData`, data, {
    responseType: 'blob'
  })
// 交货数统计-采方-导出
export const exportDeliveryQuantityStatisticsApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/excelExport`, data, {
    responseType: 'blob'
  })
// 交货数统计-供方-导出
export const exportDeliveryQuantityStatisticsSupApi = (data = {}) =>
  API.post(`/contract/tenant/mould/delivery/schedule/supplierExcelExport`, data, {
    responseType: 'blob'
  })
// 交货数统计-供方-根据模具编码查询模具信息
export const queryMouldDetailByMouldCodeApi = (data = {}) =>
  API.get(`/contract/tenant/mould/delivery/schedule/queryMouldDetailByMouldCode`, data)

// -----------------------------------------------------------------------------
// 模具台账-分页查询
export const pageMouldMainApi = (data = {}) => API.post(`/contract/tenant/mould/main/query`, data)
// 模具台账-保存
export const saveMouldMainApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/saveOrUpData`, data)
// 模具台账-导出
export const exportMouldMainApi = (data = {}, includeColumnFiledNames) =>
  API.post(
    `/contract/tenant/mould/main/excelExport?includeColumnFiledNames=${includeColumnFiledNames}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 模具台账-详情
export const detailMouldMainApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/queryDetailById`, data)
// 模具台账-更新状态
export const updateStatusMouldMainApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/updateManagementState`, data)
// 模具台账-批量编辑
export const batchSaveMouldMainApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/batchSave`, data)
// 模具台账-同步MES
export const syncMouldMainApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/button/pushMes`, data)
// 模具台账-更新工序
export const updateProcedureMouldMainApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/updateProcedure`, data)
// 模具台账-根据供应商租户id查询模具地址信息
export const getDetailAddressBySupplierTenantIdApi = (data = {}) =>
  API.get(`/contract/tenant/mould/main/getDetailAddressBySupplierTenantId`, data)
// 模具台账-履历表
export const getByMouldMainIdApi = (data = {}) =>
  API.post(`/contract/tenant/mould/resume/getByMouldMainId/${data.mouldMainId}`)
// 模具台账-履历表附件
export const getMouldMainDocumentByIdApi = (data = {}) =>
  API.post(`/contract/tenant/mould/resume/getMouldMainDocumentById/${data.id}`)

// 模具台账-校验金额查看权限
export const verifyPermissionApi = () => API.post(`/contract/tenant/mould/main/verifyPermission`)

// 模具台账-获取金额
export const getAmountByCodeApi = (data = {}) =>
  API.get(`/srm-purchase-execute/common/dataDesensitize?desensitize=${data}`)

// -----------------------------------------------------------------------------
// 模具履历记录-维修-分页查询
export const pageFixMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/mold/fix/pageList`, data)
// 模具履历记录-维修-明细
export const detailFixMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/mold/fix/detail/getByFixId`, data)
// 模具履历记录-维修-设备
export const equipmentFixMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/mold/fix/replace/equipment/getByFixId`, data)
// 模具履历记录-维修-导出
export const exportFixMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/excelExport`, data, {
    responseType: 'blob'
  })
// 模具履历记录-保养-分页查询
export const pageMaintenanceMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/tv/maintenance/record/pageList`, data)
// 模具履历记录-保养-明细
export const detailMaintenanceMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/maintenance/standard/project/getByMaintenanceStandardId`, data)
// 模具履历记录-保养-导出
export const exportMaintenanceMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/excelExport`, data, {
    responseType: 'blob'
  })
// 模具履历记录-调拨-分页查询
export const pageAllotMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/allot/pageList`, data)
// 模具履历记录-调拨明细
export const detailAllotMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/allot/detail/getByAllotId`, data)
// 模具履历记录-模具明细
export const mouldDetailAllotMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/allot/mould/getByAllotId`, data)
// 模具履历记录-镶件明细
export const insertDetailAllotMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/allot/insert/getByAllotId`, data)
// 模具履历记录-调拨-导出
export const exportAllotMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/main/excelExport`, data, {
    responseType: 'blob'
  })
// 模具履历记录-调拨-签批记录
export const logAllotMoldApi = (data = {}) =>
  API.post(`/contract/tenant/mould/t/oa/approve/log/getByBusinessId`, data)
// 模具履历记录-附件
export const attachmentInfoMoldApi = (data = {}) =>
  API.post(`/contract/tenant/file/t/attachment/info/getBusinessId`, data)
// -----------------------------------------------------------------------------
// 模具工作台 - 数据统计
export const queryWorkbenchMouldCountApi = (data = {}) =>
  API.get(`/contract/tenant/mould/main/queryWorkbenchMouldCount`, data)
// 模具工作台 - 待办
export const queryMouldListApi = (data = {}) =>
  API.get(`/contract/tenant/mould/message/queryBuyerMouldList`, data)
