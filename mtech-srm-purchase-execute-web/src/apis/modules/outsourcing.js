import { API } from '@mtech-common/http'
// import utils from "@/utils/utils";

import { BASE_TENANT, SUPPLIER_BASE_TENANT } from '@/utils/constant'

export const NAME = 'outsourcing'

// 供方

// 供方查询采方的订单bom
export const orderBom = (data = {}) => API.post(`${BASE_TENANT}/wms/stock/orderBom`, data)
// 查询订单bom-批量返回库存信息
export const supplierOrderBomStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/supplier/orderBomStock`, data)
export const supplierOutReceiveOrderBomStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/orderBomStock`, data)

// 查询订单bom-批量返回库存信息 退货
export const CancelOrderSupplierOrderBomStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/orderBomStock`, data)

// 查询订单bom-批量返回库存信息 调拨
export const AllocationOrderSupplierOrderBomStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/supplier/orderBomStock`, data)
// 创建-编辑委外退货单
export const supplierSave = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/save`, data)

// 创建-编辑委外退货单 new
export const supplierNewSave = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/supplier/save`, data)

// 创建-编辑委外调拨
export const outAllocationOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/supplier/save`, data)

// 创建-编辑委外调拨  new
export const outNewAllocationOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/supplier/save`, data)

//保存-编辑委外退货单明细--单行
export const outBuyerSaveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/buyer/saveDetail`, data)

// 查询库存数、最大领料数
export const supplierQueryStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/queryStock`, data)

// 取消委外退货单
export const outCancelOrderSupplierCancel = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/supplier/cancel`, data)

// 取消委外退货单 new
export const outNewCancelOrderSupplierCancel = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/supplier/cancel`, data)

// 委外领料明细导出-采方
export const purchaseNewExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outReceiveOrder/buyer/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 打印委外退货单
export const outCancelOrderSupplierPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/print`, data, {
    responseType: 'blob'
  })

// 打印委外退货单
export const outNewCancelOrderSupplierPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/supplier/print`, data, {
    responseType: 'blob'
  })

// 委外退货明细导出-供方
export const supplierOutCancelOrderNewExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outCancelOrder/supplier/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 打印委外调拨单
export const outAllocationOrderPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/supplier/print`, data, {
    responseType: 'blob'
  })

// 打印委外调拨单
export const outNewAllocationOrderPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/supplier/print`, data, {
    responseType: 'blob'
  })

// 打印委外调拨单 采方
export const outAllocationOrderBuyerPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/buyer/print`, data, {
    responseType: 'blob'
  })

// 打印委外退货单 采方
export const outCancelOrderBuyerPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/buyer/print`, data, {
    responseType: 'blob'
  })

// 委外退货明细导出-采方
export const outCancelOrderBuyerExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outCancelOrder/buyer/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 打印委外退货单
export const outReceiveOrderSupplierPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/print`, data, {
    responseType: 'blob'
  })

// 打印委外领料单 采方
export const outReceiveOrderBuyerPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/buyer/print`, data, {
    responseType: 'blob'
  })

// 打印委外退货单 new
export const outNewReceiveOrderSupplierPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/supplier/print`, data, {
    responseType: 'blob'
  })

// 删除委外退货单
export const outCancelOrderSupplierDelete = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/supplier/delete`, data)

// 删除委外退货单
export const outNewCancelOrderSupplierDelete = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/supplier/delete`, data)

// 委外退货单管理-供方-供应商确认
export const outCancelOrderMaterialConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/material/confirm`, data)

// 委外退货单管理-供方-供应商确认 new
export const outNewCancelOrderMaterialConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/material/confirm`, data)

// 委外退货单管理-供方-供应商确认并更新 new
export const outNewCancelOrderMaterialconfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/material/confirmAndUpdate`, data)

// 委外调拨单管理-供方-调入供应商确认
export const outAllocationOrderConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/inSupplier/confirm`, data)

// 委外调拨单管理-供方-调入供应商确认 new
export const outNewAllocationOrderConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/inSupplier/confirm`, data)

// 委外调拨单管理-供方-调入供应商明细导出 new
export const outNewAllocationOrderExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outAllocationOrder/inSupplier/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 委外调拨单管理-供方-调入供应商确认并更新
export const outAllocationOrderConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/inSupplier/confirmAndUpdate`, data)

// 委外调拨单管理-供方-调入供应商确认并更新
export const outNewAllocationOrderConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/inSupplier/confirmAndUpdate`, data)

// 委外调拨单管理-供方-调入供应商退回
export const outAllocationOrderReject = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/inSupplier/reject`, data)

// 委外调拨单管理-供方-调入供应商退回
export const outNewAllocationOrderReject = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/inSupplier/reject`, data)

// 委外调拨单管理-采方-委外调拨详情
export const outAllocationOrderBuyerGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/buyer/getOne`, data)

// 委外调拨单管理-采方-委外调拨详情new
export const outNewAllocationOrderBuyerGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/buyer/getOne`, data)

// 委外调拨单管理-采方-采方确认
export const outAllocationOrderBuyerConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/buyer/confirm`, data)

// 委外调拨单管理-采方-采方确认 new
export const outNewAllocationOrderBuyerConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/buyer/confirm`, data)

// 委外调拨单管理-采方-采方确认并更新
export const outAllocationOrderBuyerConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/buyer/confirmAndUpdate`, data)

// 委外调拨单管理-采方-采方确认并更新 new
export const outNewAllocationOrderBuyerConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/buyer/confirmAndUpdate`, data)

// 委外调拨单管理-采方-采方退回
export const outAllocationOrderBuyerReject = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/buyer/reject`, data)

// 委外调拨单管理-采方-采方退回 new
export const outNewAllocationOrderBuyerReject = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/buyer/reject`, data)

// 委外调拨单管理-采方-手工同步wms
export const outAllocationOrderBuyerSyncWms = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/buyer/syncWms`, data)

// 委外调拨单管理-采方-手工同步wms new
export const outNewAllocationOrderBuyerSyncWms = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/buyer/syncWms`, data)

// 委外调拨单明细导出-采方
export const outNewAllocationOrderBuyerExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outAllocationOrder/buyer/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 委外调拨单明细导出-供方
export const outNewAllocationOrderSupplierExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outAllocationOrder/supplier/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 委外退货单管理-供方-供应商退回
export const outCancelOrderMaterialReject = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/material/reject`, data)

// 委外退货单管理-供方-供应商退回 new
export const outNewCancelOrderMaterialReject = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/material/reject`, data)

// 委外调拨单管理-供方-委外调拨详情
export const outAllocationOrderInSupplierGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/inSupplier/getOne`, data)

// 委外调拨单管理-供方-委外调拨详情
export const outNewAllocationOrderInSupplierGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/inSupplier/getOne`, data)

// 委外退货单详情
export const supplierGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/supplier/getOne`, data)

// 委外退货单详情 new
export const supplierNewGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/supplier/getOne`, data)

// 委外调拨单详情
export const outAllocationOrderGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/supplier/getOne`, data)

// 委外调拨单详情
export const outAllocationNewOrderGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/supplier/getOne`, data)

// 委外领料单头视图
export const queryView = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/queryView`, data)

// 提交委外退货单
export const outSupplierSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/supplier/submit`, data)

// 提交委外退货单 new
export const outSupplierNewSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/supplier/submit`, data)

// 提交委外退货单
export const outNewSupplierSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/supplier/submit`, data)

// 提交委外调拨单
export const outAllocationOrderSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/supplier/submit`, data)

// 提交委外调拨单
export const outNewAllocationOrderSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/supplier/submit`, data)

// 删除委外调拨单
export const outAllocationOrderDelete = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/supplier/delete`, data)

// 删除委外调拨单 new
export const outAlloNewcationOrderDelete = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/supplier/delete`, data)

// 取消委外调拨单
export const outAllocationOrderAllocation = (data = {}) =>
  API.get(`${BASE_TENANT}/outAllocationOrder/supplier/allocation`, data)

// 取消委外调拨单 new
export const outNewAllocationOrderAllocation = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outAllocationOrder/supplier/allocation`, data)

// 取消委外调拨单 new
export const outNewAllocationOutCancelOrder = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/buyer/cancel`, data)

// 查公司
export const fuzzyQuery = (data = {}) =>
  API.post(`/masterDataManagement/tenant/customer/fuzzy-query`, data)
export const customerQuery = (data = {}) =>
  API.post(`/masterDataManagement/tenant/customer/customer-info-criteria-query`, data)

// 手工同步领料
export const buyerSyncWms = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/buyer/syncWms`, data)

// 空调调料单取消采方
export const buyerCancel = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/buyer/cancel`, data)

// 手工同步领料 new
export const buyerNewSyncWms = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/buyer/syncWms`, data)

// 手工同步退货
export const cancelBuyerSyncWms = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/buyer/syncWms`, data)

// 手工同步退货
export const cancelNewBuyerSyncWms = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/buyer/syncWms`, data)

// // 查询库存数、最大领料数
export const queryStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/queryWmsStock`, data)

// 查询库存数、最大领料数 调拨
export const queryWmsStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outAllocationOrder/supplier/queryWmsStock`, data)

// 查询库存数、最大领料数(新)
export const supplierQueryWmsStock = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/queryWmsStock`, data)
// 查询最大需求数
export const supplierQueryMaxDemand = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/queryMaxDemand`, data)

// 创建委外领料单-供方
export const addPicking = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/save`, data)

// 创建委外领料单-供方-手工创建
export const addHandPicking = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/manualSave`, data)

// 创建委外领料单-供方 new
export const addNewPicking = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/supplier/save`, data)

// 委外领料单详情-供方
export const getOnePicking = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/supplier/getOne`, data)

// 委外领料单详情-供方 new
export const getOneNewPicking = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/supplier/getOne`, data)

// 删除委外领料单明细-供方
export const deleteDetail = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/supplier/deleteDetail`, data)

// 委外领料单-根据单号查物料-供方
export const getOrderItemBom = (data = {}) => API.get(`${BASE_TENANT}/order_item_bom/${data}`)

// 提交委外领料单-供方
export const supplierSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/supplier/submit`, data)

// 提交委外领料单-供方 new
export const supplierNewSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/supplier/submit`, data)

// 取消委外领料单-供方
export const supplierCancel = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/supplier/cancel`, data)

// 取消委外领料单-供方
export const supplierNewCancel = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/supplier/cancel`, data)

// 委外领料明细导出-供方
export const supplierNewExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outReceiveOrder/supplier/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 删除委外领料单-供方
export const supplierDelete = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/supplier/delete`, data)

// 删除委外领料单-供方
export const supplierNewDelete = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/supplier/delete`, data)

// 供应商确认-退货单-原材料供方
export const materialConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/material/confirm`, data)

// 供应商确认-退货单-原材料供方 new
export const materialNewConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/material/confirm`, data)

// 供应商驳回-退货单-原材料供方
export const materialReject = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/material/reject`, data)

// 供应商驳回-退货单-原材料供方 new
export const materialNewReject = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/material/reject`, data)

// 详情-退货单-原材料供方
export const materialGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/material/getOne`, data)

// 详情-退货单-原材料供方
export const materialNewGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/material/getOne`, data)

// 委外退货单管理-供方-编辑委外退货单明细--单行-原材料
export const materialSaveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/material/saveDetail`, data)

// 委外退货单管理-供方-编辑委外退货单明细--单行
export const supplierSaveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/saveDetail`, data)

// 业务类型列表
export const getBusinessConfig = (data = {}) => API.post(`${BASE_TENANT}/pe/business/configs`, data)

// 采方

// 委外领料单管理-采方-委外领料单详情
export const buyerGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/buyer/getOne`, data)

// 委外领料单管理-采方-委外领料单详情 new
export const buyerNewGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/buyer/getOne`, data)

// 委外领料单管理-采方-采方驳回
export const buyerReject = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/buyer/reject`, data)

// 委外领料单管理-采方-采方驳回
export const buyerNewReject = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/buyer/reject`, data)

// 委外领料单管理-采方-采方确认
export const buyerConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/outReceiveOrder/buyer/confirm`, data)

// 委外领料单管理-采方-采方确认 new
export const buyerNewConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/buyer/confirm`, data)

// 委外领料单管理-采方-采方确认更新
export const buyerConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/buyer/confirmAndUpdate`, data)

// 委外领料单管理-采方-采方确认更新
export const buyerNewConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/buyer/confirmAndUpdate`, data)

// 委外领料单管理-采方-编辑委外领料单明细--单行
export const buyerSaveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/buyer/saveDetail`, data)

// 委外退货单管理-采方-委外退货单详情
export const OrderBuyerGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/buyer/getOne`, data)

// 委外退货单管理-采方-委外退货单详情 new
export const OrderNewBuyerGetOne = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/buyer/getOne`, data)

// 委外退货单管理-采方-采方驳回
export const OrderBuyerReject = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/buyer/reject`, data)

// 委外退货单管理-采方-采方驳回 new
export const OrderNewBuyerReject = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/buyer/reject`, data)

// 委外退货单管理-采方-采方确认
export const OrderBuyerConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/outCancelOrder/buyer/confirm`, data)

// 委外退货单管理-采方-采方确认 new
export const OrderNewBuyerConfirm = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outCancelOrder/buyer/confirm`, data)

// 委外退货单管理-采方-采方确认  明细 new
export const OrderNewDetailBuyerConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/buyer/confirmAndUpdateBatch`, data)

// 委外退货单管理-供方-采方确认  明细 new
export const materialNewDetailBuyerConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/material/confirmAndUpdateBatch`, data)

// 委外直退明细导出-供方
export const materialNewDetailSupplierNewExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/kt/outCancelOrder/material/item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 委外调拨单管理-采方-采方确认  明细 new
export const OrderNewDetailBuyerConfirmAndUpdateBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/buyer/confirmAndUpdateBatch`, data)
// 委外调拨单管理-供方-供方确认  明细 new
export const OrderNewDetailInSupplierConfirmAndUpdateBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/inSupplier/confirmAndUpdateBatch`, data)

// 委外退货单管理-采方-采方确认以及更新
export const OrderBuyerConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/buyer/confirmAndUpdate`, data)

// 委外退货获取库存
export const outWaitQuerySapOutDemand = (data = {}) =>
  API.post(`${BASE_TENANT}/outCancelOrder/supplier/querySapOutDemand`, data)

// 委外退货单管理-采方-采方确认以及更新 new
export const OrderNewBuyerConfirmAndUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/buyer/confirmAndUpdate`, data)

// 委外退货单管理-采方-编辑委外退货单明细--单行
export const OrderBuyerSaveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/buyer/saveDetail`, data)

// 调拨 委外需求量
export const OutQuerySapOutDemand = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/supplier/querySapOutDemand`, data)

// 领料 委外需求量
export const QuerySapOutDemand = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/supplier/querySapOutDemand`, data)
/**
 * 委外领料数量限制配置
 * 采访 -租户级 - 委外库存限制配置
 */
// 列表
export const getStockSetting = (data = {}) =>
  API.post(`${BASE_TENANT}/stockSetting/buyer/query`, data)

// 保存
export const saveStockSetting = (data = {}) =>
  API.post(`${BASE_TENANT}/stockSetting/buyer/save`, data)

// 保存kt new
export const saveNewStockSetting = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/stockSetting/buyer/save`, data)

// 删除
export const deleteStockSetting = (data = {}) =>
  API.get(`${BASE_TENANT}/stockSetting/buyer/delete`, data)

// 删除 kt new
export const deleteNewStockSetting = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/stockSetting/buyer/delete`, data)

// 启用禁用
export const activeStockSetting = (data = {}) =>
  API.get(`${BASE_TENANT}/stockSetting/buyer/changeStatus`, data)

// 启用禁用 kt new
export const activeNewStockSetting = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/stockSetting/buyer/changeStatus`, data)

// 未用到：详情
export const detailStockSetting = (data = {}) =>
  API.post(`${BASE_TENANT}/stockSetting/buyer/detail`, data)

//供方汇总查询采方委外待领料明细数据
export const supplierCollectQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/outWaitReceiveOrder/supplier/collectQuery`, data)
// 供方过滤查询委外领料单明细
export const outWaitReceiveOrderQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/outWaitReceiveOrder/supplier/query`, data)
// 供方过滤查询委外领料单明细  new 查询sap委外需求量
export const outNewWaitReceiveOrderQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/supplier/querySapOutDemand`, data)

//  new 查询sap委外需求量 退货
export const outNewWaitQuerySapOutDemand = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/supplier/querySapOutDemand`, data)

// 领料供方创建  new
export const ktOutReceiveOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/supplier/save`, data)

//根据待领料明细创建-编辑委外领料单
export const supplierSaveByWaitOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/outReceiveOrder/supplier/saveByWaitOrder`, data)

//供应商带地址
export const supplierCancelAddress = (data = {}) =>
  API.post(`${SUPPLIER_BASE_TENANT}/buyer/partner/getAddressBySupplierCode`, data)

// 模板下载
export const outReceiveOrderTemplate = (data = {}) =>
  API.get(`${BASE_TENANT}/kt/outReceiveOrder/supplier/template`, data, {
    responseType: 'blob'
  })

// 调拨导入
export const outAllocationOrderImport = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/supplier/import`, data, {
    responseType: 'blob'
  })

// 领料导入
export const outReceiveOrderImport = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outReceiveOrder/supplier/import`, data, {
    responseType: 'blob'
  })

// 退货导入
export const outCancelOrderImport = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/supplier/import`, data, {
    responseType: 'blob'
  })

// 采方-普通物料-委外退货单-创建委外退货单-保存
export const createReturnBillSave = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/buyer/save`, data)

// 采方-普通物料-委外退货单-创建委外退货单-保存
export const createReturnBillSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outCancelOrder/buyer/submit`, data)

// 采方-普通物料-委外退货单-创建委外退货单-保存
export const createTransferBillSave = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/buyer/save`, data)

// 采方-普通物料-委外退货单-创建委外退货单-保存
export const createTransferBillSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/kt/outAllocationOrder/buyer/submit`, data)
